import os
import stat
import uuid
from datetime import datetime
from flask import current_app, request
from werkzeug.utils import secure_filename

def allowed_file(filename):
    """Check if the file extension is allowed for event images."""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_event_images_dir():
    """Get the base directory for event images."""
    try:
        # Create event_images directory in the application root
        event_images_dir = os.path.join(current_app.root_path, 'static', 'event_images')
        if not os.path.exists(event_images_dir):
            os.makedirs(event_images_dir, exist_ok=True)
            # Set proper permissions: owner read/write/execute, group read/execute, others read/execute
            os.chmod(event_images_dir, stat.S_IRWXU | stat.S_IRGRP | stat.S_IXGRP | stat.S_IROTH | stat.S_IXOTH)
        
        return event_images_dir
    except Exception as e:
        current_app.logger.error(f"Error creating event images directory: {str(e)}")
        raise

def get_user_event_images_dir(username):
    """Get the event images directory for a specific user."""
    try:
        base_dir = get_event_images_dir()
        user_dir = os.path.join(base_dir, username)
        
        if not os.path.exists(user_dir):
            os.makedirs(user_dir, exist_ok=True)
            # Set proper permissions: owner read/write/execute, group read/execute, others read/execute
            os.chmod(user_dir, stat.S_IRWXU | stat.S_IRGRP | stat.S_IXGRP | stat.S_IROTH | stat.S_IXOTH)
        
        return user_dir
    except Exception as e:
        current_app.logger.error(f"Error creating user event images directory for {username}: {str(e)}")
        raise

def generate_unique_filename(original_filename):
    """Generate a unique filename to prevent conflicts."""
    # Get file extension
    file_ext = ''
    if '.' in original_filename:
        file_ext = '.' + original_filename.rsplit('.', 1)[1].lower()
    
    # Generate unique filename with timestamp and UUID
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    unique_id = str(uuid.uuid4())[:8]  # Use first 8 characters of UUID
    
    return f"event_{timestamp}_{unique_id}{file_ext}"

def save_event_image(file, username):
    """
    Save an event image file and return the URL and filename.
    
    Args:
        file: The uploaded file object
        username: The username of the user uploading the image
    
    Returns:
        tuple: (success, result) where result is either {'url': str, 'filename': str} or error message
    """
    try:
        if not file or not allowed_file(file.filename):
            return False, "Invalid file type. Allowed types: PNG, JPG, JPEG, GIF, WEBP"
        
        # Get user's event images directory
        upload_dir = get_user_event_images_dir(username)
        
        # Generate unique filename
        unique_filename = generate_unique_filename(file.filename)
        filepath = os.path.join(upload_dir, unique_filename)
        
        # Save the file
        file.save(filepath)
        
        # Set proper file permissions: owner read/write, group read, others read
        os.chmod(filepath, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
        
        # Generate the public URL
        # Use enterprise.tcgsync.com as requested
        host = request.host_url.rstrip('/')
        if 'localhost' in host or '127.0.0.1' in host:
            # For local development
            image_url = f"{host}/static/event_images/{username}/{unique_filename}"
        else:
            # For production - use enterprise.tcgsync.com
            image_url = f"https://enterprise.tcgsync.com/static/event_images/{username}/{unique_filename}"
        
        current_app.logger.info(f"Successfully saved event image for user {username}: {unique_filename}")
        
        return True, {
            'url': image_url,
            'filename': unique_filename,
            'original_filename': file.filename
        }
        
    except Exception as e:
        current_app.logger.error(f"Error saving event image for user {username}: {str(e)}")
        return False, f"Error saving image: {str(e)}"

def delete_event_image(filename, username):
    """
    Delete an event image file.
    
    Args:
        filename: The filename to delete
        username: The username of the user who owns the image
    
    Returns:
        tuple: (success, message)
    """
    try:
        if not allowed_file(filename):
            return False, "Invalid file type"
        
        upload_dir = get_user_event_images_dir(username)
        filepath = os.path.join(upload_dir, filename)
        
        if not os.path.exists(filepath):
            return False, "File not found"
        
        os.remove(filepath)
        current_app.logger.info(f"Successfully deleted event image for user {username}: {filename}")
        
        return True, "Image deleted successfully"
        
    except Exception as e:
        current_app.logger.error(f"Error deleting event image {filename} for user {username}: {str(e)}")
        return False, f"Error deleting image: {str(e)}"

def get_image_url(filename, username):
    """
    Get the public URL for an event image.
    
    Args:
        filename: The filename of the image
        username: The username of the user who owns the image
    
    Returns:
        str: The public URL for the image
    """
    host = request.host_url.rstrip('/')
    if 'localhost' in host or '127.0.0.1' in host:
        # For local development
        return f"{host}/static/event_images/{username}/{filename}"
    else:
        # For production - use enterprise.tcgsync.com
        return f"https://enterprise.tcgsync.com/static/event_images/{username}/{filename}"
