/* Inventory Dashboard Styles */

/* Card color variations */
.setup-card:nth-child(1) .setup-card-header i {
    color: #e74c3c; /* View Inventory - Red */
}

.setup-card:nth-child(2) .setup-card-header i {
    color: #2ecc71; /* Add Catalog - Green */
}

.setup-card:nth-child(3) .setup-card-header i {
    color: #3498db; /* Warehouse - Blue */
}

.setup-card:nth-child(4) .setup-card-header i {
    color: #e74c3c; /* Locations - Red */
}

.setup-card:nth-child(5) .setup-card-header i {
    color: #8e44ad; /* Barcodes - Purple */
}

.setup-card:nth-child(6) .setup-card-header i {
    color: #9b59b6; /* Card Scanning - Purple */
}

.setup-card:nth-child(7) .setup-card-header i {
    color: #f39c12; /* CSV Upload - Orange */
}

.setup-card:nth-child(8) .setup-card-header i {
    color: #1abc9c; /* Manual Entry - Turquoise */
}

.setup-card:nth-child(9) .setup-card-header i {
    color: #e67e22; /* Repricing - Orange */
}

/* Button color variations */
.setup-card:nth-child(1) .setup-card-footer .btn {
    background-color: #e74c3c;
    border-color: #e74c3c;
}

.setup-card:nth-child(2) .setup-card-footer .btn {
    background-color: #2ecc71;
    border-color: #2ecc71;
}

.setup-card:nth-child(3) .setup-card-footer .btn {
    background-color: #3498db;
    border-color: #3498db;
}

.setup-card:nth-child(4) .setup-card-footer .btn {
    background-color: #e74c3c;
    border-color: #e74c3c;
}

.setup-card:nth-child(5) .setup-card-footer .btn {
    background-color: #8e44ad;
    border-color: #8e44ad;
}

.setup-card:nth-child(6) .setup-card-footer .btn {
    background-color: #9b59b6;
    border-color: #9b59b6;
}

.setup-card:nth-child(7) .setup-card-footer .btn {
    background-color: #f39c12;
    border-color: #f39c12;
}

.setup-card:nth-child(8) .setup-card-footer .btn {
    background-color: #1abc9c;
    border-color: #1abc9c;
}

.setup-card:nth-child(9) .setup-card-footer .btn {
    background-color: #e67e22;
    border-color: #e67e22;
}

/* Hover effects for buttons */
.setup-card:nth-child(1) .setup-card-footer .btn:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

.setup-card:nth-child(2) .setup-card-footer .btn:hover {
    background-color: #27ae60;
    border-color: #27ae60;
}

.setup-card:nth-child(3) .setup-card-footer .btn:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.setup-card:nth-child(4) .setup-card-footer .btn:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

.setup-card:nth-child(5) .setup-card-footer .btn:hover {
    background-color: #7d3c98;
    border-color: #7d3c98;
}

.setup-card:nth-child(6) .setup-card-footer .btn:hover {
    background-color: #8e44ad;
    border-color: #8e44ad;
}

.setup-card:nth-child(7) .setup-card-footer .btn:hover {
    background-color: #d35400;
    border-color: #d35400;
}

.setup-card:nth-child(8) .setup-card-footer .btn:hover {
    background-color: #16a085;
    border-color: #16a085;
}

.setup-card:nth-child(9) .setup-card-footer .btn:hover {
    background-color: #d35400;
    border-color: #d35400;
}

/* Subtle border glow effects on hover */
.setup-card:nth-child(1):hover {
    box-shadow: 0 8px 30px rgba(231, 76, 60, 0.2);
    border-color: rgba(231, 76, 60, 0.3);
}

.setup-card:nth-child(2):hover {
    box-shadow: 0 8px 30px rgba(46, 204, 113, 0.2);
    border-color: rgba(46, 204, 113, 0.3);
}

.setup-card:nth-child(3):hover {
    box-shadow: 0 8px 30px rgba(52, 152, 219, 0.2);
    border-color: rgba(52, 152, 219, 0.3);
}

.setup-card:nth-child(4):hover {
    box-shadow: 0 8px 30px rgba(231, 76, 60, 0.2);
    border-color: rgba(231, 76, 60, 0.3);
}

.setup-card:nth-child(5):hover {
    box-shadow: 0 8px 30px rgba(142, 68, 173, 0.2);
    border-color: rgba(142, 68, 173, 0.3);
}

.setup-card:nth-child(6):hover {
    box-shadow: 0 8px 30px rgba(155, 89, 182, 0.2);
    border-color: rgba(155, 89, 182, 0.3);
}

.setup-card:nth-child(7):hover {
    box-shadow: 0 8px 30px rgba(243, 156, 18, 0.2);
    border-color: rgba(243, 156, 18, 0.3);
}

.setup-card:nth-child(8):hover {
    box-shadow: 0 8px 30px rgba(26, 188, 156, 0.2);
    border-color: rgba(26, 188, 156, 0.3);
}

.setup-card:nth-child(9):hover {
    box-shadow: 0 8px 30px rgba(230, 126, 34, 0.2);
    border-color: rgba(230, 126, 34, 0.3);
}

/* Animation for the check icons */
.setup-features-list li i {
    transition: transform 0.3s ease;
}

.setup-card:hover .setup-features-list li i {
    transform: scale(1.2);
}

/* Staggered animation for list items on hover */
.setup-card:hover .setup-features-list li:nth-child(1) i {
    transition-delay: 0.05s;
}

.setup-card:hover .setup-features-list li:nth-child(2) i {
    transition-delay: 0.1s;
}

.setup-card:hover .setup-features-list li:nth-child(3) i {
    transition-delay: 0.15s;
}

.setup-card:hover .setup-features-list li:nth-child(4) i {
    transition-delay: 0.2s;
}

/* Section header styling */
.section-header {
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.section-title {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.section-subtitle {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.875rem;
    margin-bottom: 0;
}

/* Coming soon badge styling */
.coming-soon-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    z-index: 10;
}

/* Locked card styling */
.card-locked {
    position: relative;
    opacity: 0.7;
    cursor: not-allowed;
}

.card-locked .btn {
    pointer-events: none;
    opacity: 0.6;
}

.card-locked::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    z-index: 5;
}

/* Setup Header Styles */
.setup-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.setup-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.setup-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.setup-card {
    background: linear-gradient(135deg, rgba(26, 32, 44, 0.8), rgba(45, 55, 72, 0.8));
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.setup-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
    border-color: rgba(255, 255, 255, 0.1);
}

.setup-card-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.setup-card-header i {
    font-size: 2rem;
    color: var(--primary-color);
}

.setup-card-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
}

.setup-card-body {
    padding: 1.5rem;
    flex-grow: 1;
}

.setup-card-body p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
}

.setup-features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.setup-features-list li {
    margin-bottom: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.setup-features-list li i {
    color: var(--success-color);
    font-size: 0.9rem;
}

.setup-card-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    text-align: center;
}

.setup-card-footer .btn {
    width: 100%;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.setup-card-footer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .setup-grid {
        grid-template-columns: 1fr;
    }
    
    .setup-header h1 {
        font-size: 1.75rem;
    }
}
