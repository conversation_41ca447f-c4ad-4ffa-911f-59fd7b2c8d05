{% extends "base.html" %}
{% block title %}Inventory Dashboard{% endblock %}
{% block content %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/inventory.css') }}">

<div class="container-fluid dashboard-container">
    {% if not current_user.subscription or current_user.subscription.name == 'Free' %}
    <div class="alert alert-primary mt-1 mb-2 py-2 d-flex justify-content-between align-items-center" role="alert" style="font-size: 0.9rem;">
        <div class="text-center w-100">
            <i class="fas fa-star me-1"></i>
            <strong>Upgrade your plan to unlock all features!</strong> Get access to advanced analytics, premium tools, and more.
        </div>
        <a href="{{ url_for('activation.activating') }}" class="btn btn-primary btn-sm py-1 px-2">
            Upgrade Now
        </a>
    </div>
    {% endif %}

    <!-- Inventory Dashboard Header -->
    <div class="setup-header">
        <h1><i class="fas fa-boxes me-2"></i>Inventory Dashboard</h1>
        <p class="text-muted">Manage your inventory, warehouse operations, and product catalog</p>
    </div>
    <!-- Core Inventory Management Section -->
    <div class="section-header">
        <h2 class="section-title">
            <i class="fas fa-boxes me-2" style="color: #e74c3c;"></i>
            Core Inventory Management
        </h2>
        <p class="section-subtitle">Essential tools for viewing your inventory and managing your product catalog</p>
    </div>

    <div class="setup-grid">
        <!-- View Inventory Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-boxes"></i>
                <h2>View Inventory</h2>
            </div>
            <div class="setup-card-body">
                <p>Browse and manage your current inventory. View stock levels, prices, and detailed product information.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> View all inventory items</li>
                    <li><i class="fas fa-check-circle"></i> Filter by game, set, or condition</li>
                    <li><i class="fas fa-check-circle"></i> Update prices and quantities</li>
                    <li><i class="fas fa-check-circle"></i> Export inventory data</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('products.products') }}" class="btn btn-primary">
                    <i class="fas fa-eye me-2"></i>View Inventory
                </a>
            </div>
        </div>

        <!-- Add Catalog Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-plus-circle"></i>
                <h2>Add Catalog</h2>
            </div>
            <div class="setup-card-body">
                <p>Update your product catalog with new items. Add detailed information and manage product variants.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Add new products</li>
                    <li><i class="fas fa-check-circle"></i> Set competitive prices</li>
                    <li><i class="fas fa-check-circle"></i> Manage product variants</li>
                    <li><i class="fas fa-check-circle"></i> Bulk catalog updates</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('update_catalog.update_catalog') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add Catalog
                </a>
            </div>
        </div>
    </div>

    <!-- Warehouse Operations Section -->
    <div class="section-header mt-5">
        <h2 class="section-title">
            <i class="fas fa-warehouse me-2" style="color: #3498db;"></i>
            Warehouse Operations
        </h2>
        <p class="section-subtitle">Physical inventory management, location tracking, and warehouse organization</p>
    </div>

    <div class="setup-grid">
        <!-- Warehouse Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-warehouse"></i>
                <h2>Warehouse</h2>
            </div>
            <div class="setup-card-body">
                <p>Manage comprehensive warehouse operations. Process inventory movements and update stock levels.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Process inventory movements</li>
                    <li><i class="fas fa-check-circle"></i> Handle staging areas</li>
                    <li><i class="fas fa-check-circle"></i> Update stock levels</li>
                    <li><i class="fas fa-check-circle"></i> Track inventory changes</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('warehouse.warehouse') }}" class="btn btn-primary">
                    <i class="fas fa-dolly me-2"></i>Warehouse Operations
                </a>
            </div>
        </div>

        <!-- Locations Card -->
        <div class="setup-card card-locked">
            <div class="coming-soon-badge">Coming Soon</div>
            <div class="setup-card-header">
                <i class="fas fa-map-marker-alt"></i>
                <h2>Locations</h2>
            </div>
            <div class="setup-card-body">
                <p>Manage warehouse and store locations. Track where items are situated for efficient organization.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Create location hierarchies</li>
                    <li><i class="fas fa-check-circle"></i> Assign products to locations</li>
                    <li><i class="fas fa-check-circle"></i> Track location history</li>
                    <li><i class="fas fa-check-circle"></i> Generate location reports</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('inventory.locations') }}" class="btn btn-primary">
                    <i class="fas fa-map-marker-alt me-2"></i>Manage Locations
                </a>
            </div>
        </div>

        <!-- Barcodes Card -->
        <div class="setup-card card-locked">
            <div class="coming-soon-badge">Coming Soon</div>
            <div class="setup-card-header">
                <i class="fas fa-barcode"></i>
                <h2>Barcodes</h2>
            </div>
            <div class="setup-card-body">
                <p>Manage barcode scanning and generation. Create, print, and scan barcodes for efficient tracking.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Generate product barcodes</li>
                    <li><i class="fas fa-check-circle"></i> Print barcode labels</li>
                    <li><i class="fas fa-check-circle"></i> Scan for quick lookups</li>
                    <li><i class="fas fa-check-circle"></i> Batch barcode operations</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('inventory.barcodes') }}" class="btn btn-primary">
                    <i class="fas fa-barcode me-2"></i>Manage Barcodes
                </a>
            </div>
        </div>
    </div>

    <!-- Data Import Methods Section -->
    <div class="section-header mt-5">
        <h2 class="section-title">
            <i class="fas fa-upload me-2" style="color: #9b59b6;"></i>
            Data Import Methods
        </h2>
        <p class="section-subtitle">Various ways to add products to your inventory efficiently</p>
    </div>

    <div class="setup-grid">
        <!-- Card Scanning Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-camera"></i>
                <h2>Card Scanning</h2>
            </div>
            <div class="setup-card-body">
                <p>Scan trading cards to quickly add them to your inventory. Advanced AI automatically identifies cards.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> AI-powered card recognition</li>
                    <li><i class="fas fa-check-circle"></i> Automatic price suggestions</li>
                    <li><i class="fas fa-check-circle"></i> Batch scanning support</li>
                    <li><i class="fas fa-check-circle"></i> Mobile device compatible</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('card_scanning.card_scanning') }}" class="btn btn-primary">
                    <i class="fas fa-camera me-2"></i>Scan Cards
                </a>
            </div>
        </div>

        <!-- CSV Upload Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-file-csv"></i>
                <h2>CSV Upload</h2>
            </div>
            <div class="setup-card-body">
                <p>Import inventory data from CSV files. Process bulk inventory updates with multiple data formats.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Bulk inventory imports</li>
                    <li><i class="fas fa-check-circle"></i> Multiple format support</li>
                    <li><i class="fas fa-check-circle"></i> Data validation</li>
                    <li><i class="fas fa-check-circle"></i> Error reporting</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('csv.csv') }}" class="btn btn-primary">
                    <i class="fas fa-upload me-2"></i>Upload CSV
                </a>
            </div>
        </div>

        <!-- Manual Entry Card -->
        <div class="setup-card card-locked">
            <div class="coming-soon-badge">Coming Soon</div>
            <div class="setup-card-header">
                <i class="fas fa-keyboard"></i>
                <h2>Manual Entry</h2>
            </div>
            <div class="setup-card-body">
                <p>Manually add individual items to your inventory. Perfect for one-off additions with precise control.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Individual item entry</li>
                    <li><i class="fas fa-check-circle"></i> Detailed product information</li>
                    <li><i class="fas fa-check-circle"></i> Custom fields support</li>
                    <li><i class="fas fa-check-circle"></i> Image upload capability</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('inventory.manual_entry') }}" class="btn btn-primary">
                    <i class="fas fa-keyboard me-2"></i>Manual Entry
                </a>
            </div>
        </div>
    </div>

    <!-- Advanced Features Section -->
    <div class="section-header mt-5">
        <h2 class="section-title">
            <i class="fas fa-cogs me-2" style="color: #e67e22;"></i>
            Advanced Features
        </h2>
        <p class="section-subtitle">Advanced tools for pricing optimization and inventory analytics</p>
    </div>

    <div class="setup-grid">
        <!-- Repricing Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-tags"></i>
                <h2>Repricing</h2>
            </div>
            <div class="setup-card-body">
                <p>Configure automated pricing rules, condition percentages, and game-specific minimum prices.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Automated pricing rules</li>
                    <li><i class="fas fa-check-circle"></i> Condition percentages</li>
                    <li><i class="fas fa-check-circle"></i> Game-specific minimum prices</li>
                    <li><i class="fas fa-check-circle"></i> Test pricing strategies</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('shopify_autopricing.index') }}" class="btn btn-primary">
                    <i class="fas fa-tags me-2"></i>Manage Repricing
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
