/* Events Dashboard Styles */

/* Card color variations */
.setup-card:nth-child(1) .setup-card-header i {
    color: #2ecc71; /* Create Events - Green */
}

.setup-card:nth-child(2) .setup-card-header i {
    color: #e74c3c; /* View Events - Red */
}

.setup-card:nth-child(3) .setup-card-header i {
    color: #3498db; /* Attendees - Blue */
}

.setup-card:nth-child(4) .setup-card-header i {
    color: #9b59b6; /* Results - Purple */
}

.setup-card:nth-child(5) .setup-card-header i {
    color: #3498db; /* My URL - Blue */
}

/* Button color variations */
.setup-card:nth-child(1) .setup-card-footer .btn {
    background-color: #2ecc71;
    border-color: #2ecc71;
}

.setup-card:nth-child(2) .setup-card-footer .btn {
    background-color: #e74c3c;
    border-color: #e74c3c;
}

.setup-card:nth-child(3) .setup-card-footer .btn {
    background-color: #3498db;
    border-color: #3498db;
}

.setup-card:nth-child(4) .setup-card-footer .btn {
    background-color: #9b59b6;
    border-color: #9b59b6;
}

.setup-card:nth-child(5) .setup-card-footer .btn {
    background-color: #3498db;
    border-color: #3498db;
}

/* Hover effects for buttons */
.setup-card:nth-child(1) .setup-card-footer .btn:hover {
    background-color: #27ae60;
    border-color: #27ae60;
}

.setup-card:nth-child(2) .setup-card-footer .btn:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

.setup-card:nth-child(3) .setup-card-footer .btn:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.setup-card:nth-child(4) .setup-card-footer .btn:hover {
    background-color: #8e44ad;
    border-color: #8e44ad;
}

.setup-card:nth-child(5) .setup-card-footer .btn:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

/* Subtle border glow effects on hover */
.setup-card:nth-child(1):hover {
    box-shadow: 0 8px 30px rgba(46, 204, 113, 0.2);
    border-color: rgba(46, 204, 113, 0.3);
}

.setup-card:nth-child(2):hover {
    box-shadow: 0 8px 30px rgba(231, 76, 60, 0.2);
    border-color: rgba(231, 76, 60, 0.3);
}

.setup-card:nth-child(3):hover {
    box-shadow: 0 8px 30px rgba(52, 152, 219, 0.2);
    border-color: rgba(52, 152, 219, 0.3);
}

.setup-card:nth-child(4):hover {
    box-shadow: 0 8px 30px rgba(155, 89, 182, 0.2);
    border-color: rgba(155, 89, 182, 0.3);
}

.setup-card:nth-child(5):hover {
    box-shadow: 0 8px 30px rgba(52, 152, 219, 0.2);
    border-color: rgba(52, 152, 219, 0.3);
}

/* Animation for the check icons */
.setup-features-list li i {
    transition: transform 0.3s ease;
}

.setup-card:hover .setup-features-list li i {
    transform: scale(1.2);
}

/* Staggered animation for list items on hover */
.setup-card:hover .setup-features-list li:nth-child(1) i {
    transition-delay: 0.05s;
}

.setup-card:hover .setup-features-list li:nth-child(2) i {
    transition-delay: 0.1s;
}

.setup-card:hover .setup-features-list li:nth-child(3) i {
    transition-delay: 0.15s;
}

.setup-card:hover .setup-features-list li:nth-child(4) i {
    transition-delay: 0.2s;
}

/* Stats card styling */
.stats-card {
    background: linear-gradient(135deg, rgba(26, 32, 44, 0.8), rgba(45, 55, 72, 0.8));
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
    border-color: rgba(255, 255, 255, 0.1);
}

.stats-card-header {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(52, 152, 219, 0.1));
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stats-card-body {
    padding: 1.5rem;
}

.stats-counter {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* Upcoming events card styling */
.events-card {
    background: linear-gradient(135deg, rgba(26, 32, 44, 0.8), rgba(45, 55, 72, 0.8));
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    height: 100%;
}

.events-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
    border-color: rgba(255, 255, 255, 0.1);
}

.events-card-header {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.2), rgba(46, 204, 113, 0.1));
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.events-card-body {
    padding: 1.5rem;
}

.upcoming-event {
    padding: 1rem;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.05);
    margin-bottom: 1rem;
    transition: background-color 0.2s ease;
}

.upcoming-event:hover {
    background-color: rgba(255, 255, 255, 0.08);
}

.upcoming-event:last-child {
    margin-bottom: 0;
}

/* Event type badges */
.badge-tournament {
    background-color: #9b59b6;
}

.badge-pre-release {
    background-color: #f1c40f;
    color: #2c3e50;
}

.badge-draft {
    background-color: #e67e22;
}

.badge-sealed {
    background-color: #16a085;
}

.badge-casual {
    background-color: #3498db;
}

.badge-championship {
    background-color: #e74c3c;
}

.badge-other {
    background-color: #95a5a6;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: rgba(255, 255, 255, 0.7);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.3);
}

/* Setup Header Styles */
.setup-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.setup-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.setup-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.setup-card {
    background: linear-gradient(135deg, rgba(26, 32, 44, 0.8), rgba(45, 55, 72, 0.8));
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.setup-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
    border-color: rgba(255, 255, 255, 0.1);
}

.setup-card-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.setup-card-header i {
    font-size: 2rem;
    color: var(--primary-color);
}

.setup-card-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
}

.setup-card-body {
    padding: 1.5rem;
    flex-grow: 1;
}

.setup-card-body p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
}

.setup-features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.setup-features-list li {
    margin-bottom: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.setup-features-list li i {
    color: var(--success-color);
    font-size: 0.9rem;
}

.setup-card-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    text-align: center;
}

.setup-card-footer .btn {
    width: 100%;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.setup-card-footer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .setup-grid {
        grid-template-columns: 1fr;
    }
    
    .setup-header h1 {
        font-size: 1.75rem;
    }
}
