import stripe
import logging
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from utils.email_utils import send_email
from config import Config

premium_bp = Blueprint('premium', __name__)
logger = logging.getLogger(__name__)

# Initialize Stripe
stripe.api_key = Config.STRIPE_SECRET_KEY

@premium_bp.route('/api/premium/order', methods=['POST'])
@login_required
def order_premium_feature():
    """
    Handle premium feature order requests.
    This endpoint:
    1. Sends an email notification to admin
    2. Creates a Stripe checkout session
    3. Returns the payment URL
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"success": False, "message": "No data provided"}), 400
        
        # Extract order details
        product_id = data.get('product_id')
        product_name = data.get('product_name')
        price = data.get('price')
        
        if not all([product_id, product_name, price]):
            return jsonify({"success": False, "message": "Missing required fields"}), 400
        

        # Send email notification to admin
        admin_email = "<EMAIL>"
        subject = f"New Premium Feature Order: {product_name}"
        
        email_body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                <h2 style="color: #4a6ee0;">New Premium Feature Order</h2>
                <p>A user has ordered a premium feature and is proceeding to payment.</p>
                
                <h3>Order Details:</h3>
                <ul>
                    <li><strong>Feature:</strong> {product_name}</li>
                    <li><strong>Price:</strong> £{price}</li>
                    <li><strong>Username:</strong> {current_user.username}</li>
                    <li><strong>Email:</strong> {current_user.email}</li>
                </ul>
                
                <p>Please note that this feature can take up to 28 days to implement after payment is confirmed.</p>
                
                <p style="font-size: 0.9em; color: #666; margin-top: 30px;">
                    This is an automated message from the TCGSync Premium Features system.
                </p>
            </div>
        </body>
        </html>
        """
        
        # Send email
        try:
            send_email(
                username=current_user.username,
                to=admin_email,
                subject=subject,
                content=email_body,
                is_html=True
            )
            logger.info(f"Premium feature order notification email sent to admin for {product_name} by {current_user.username}")
        except Exception as e:
            logger.error(f"Error sending premium feature order email: {str(e)}")
            # Continue with checkout even if email fails
        
        # Create Stripe checkout session
        try:
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=[{
                    'price_data': {
                        'currency': 'gbp',
                        'product_data': {
                            'name': product_name,
                            'description': f'Implementation may take up to 28 days after payment',
                        },
                        'unit_amount': int(float(price) * 100),  # Convert to pence
                    },
                    'quantity': 1,
                }],
                mode='payment',
                success_url=request.host_url + 'dashboard?premium_order_success=true',
                cancel_url=request.host_url + 'dashboard?premium_order_cancelled=true',
                metadata={
                    'username': current_user.username,
                    'product_id': product_id,
                    'product_name': product_name
                }
            )
            
            return jsonify({
                "success": True, 
                "payment_url": checkout_session.url
            })
            
        except Exception as e:
            logger.error(f"Error creating Stripe checkout session: {str(e)}")
            return jsonify({"success": False, "message": f"Error creating payment session: {str(e)}"}), 500
            
    except Exception as e:
        logger.error(f"Error processing premium feature order: {str(e)}")
        return jsonify({"success": False, "message": f"Error processing order: {str(e)}"}), 500
