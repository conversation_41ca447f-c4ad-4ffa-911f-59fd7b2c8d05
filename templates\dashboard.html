{% extends "base.html" %}

{% block content %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/tcgplayer_sales.css') }}">

<div class="container-fluid dashboard-container">
    {% if not current_user.subscription or current_user.subscription.name == 'Free' %}
    <div class="alert alert-primary mt-1 mb-2 py-2 d-flex justify-content-between align-items-center" role="alert" style="font-size: 0.9rem;">
        <div class="text-center w-100">
            <i class="fas fa-star me-1"></i>
            <strong>Upgrade your plan to unlock all features!</strong> Get access to advanced analytics, premium tools, and more.
        </div>
        <a href="{{ url_for('activation.activating') }}" class="btn btn-primary btn-sm py-1 px-2">
            Upgrade Now
        </a>
    </div>
    {% endif %}

    <!-- Quick Access Section -->
    <div class="quick-access-section{% if not current_user.subscription or current_user.subscription.name == 'Free' %} mt-0{% endif %}" style="margin-bottom: 10px;">
        <!-- Header removed as requested -->
        <div class="quick-access-grid">
            {% if current_user.username == 'admintcg' %}
            <a href="{{ url_for('admin.admin_dashboard') }}" class="quick-access-card admin">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px; position: relative;">
                    <i class="fas fa-user-shield" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Admin Panel</span>
                </div>
            </a>
            {% endif %}

            <a href="{{ url_for('buylist.buylist_dashboard') }}" class="quick-access-card buylist">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="far fa-credit-card" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Buylist</span>
                </div>
            </a>

            <a href="{{ url_for('customers.customers_dashboard') }}" class="quick-access-card customers">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-user-friends" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Customers</span>
                </div>
            </a>

            <a href="{{ url_for('events.events_dashboard') }}" class="quick-access-card events">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-calendar-alt" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Events</span>
                </div>
            </a>

            <a href="{{ url_for('terminals.terminals') }}" class="quick-access-card terminals">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-desktop" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Instore Terminals</span>
                </div>
            </a>

            <a href="{{ url_for('inventory.inventory_dashboard') }}" class="quick-access-card inventory">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-boxes" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Inventory</span>
                </div>
            </a>

            <!-- Orders Card (Coming 1st June) -->
            <div class="quick-access-card orders coming-soon-card" style="position: relative; opacity: 0.7; cursor: not-allowed;">
                <div class="coming-soon-badge" style="position: absolute; top: 5px; right: 5px; background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 2px 6px; border-radius: 8px; font-size: 0.6rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); z-index: 10;">Coming 1st June</div>
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-shopping-bag" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Orders</span>
                </div>
            </div>

            <a href="{{ url_for('pos.pos') }}" class="quick-access-card pos">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-shopping-cart" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>POS</span>
                </div>
            </a>

            <!-- Release Schedule Card (Active) -->
            <a href="{{ url_for('calendar.calendar_dashboard') }}" class="quick-access-card calendar">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-calendar" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Release Schedule</span>
                </div>
            </a>

            <!-- Sellers Network Card (Coming 1st June) -->
            <div class="quick-access-card sellers-network coming-soon-card" style="position: relative; opacity: 0.7; cursor: not-allowed;">
                <div class="coming-soon-badge" style="position: absolute; top: 5px; right: 5px; background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 2px 6px; border-radius: 8px; font-size: 0.6rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); z-index: 10;">Coming 1st June</div>
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-network-wired" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Sellers Network</span>
                </div>
            </div>

            <!-- Setup Card -->
            <a href="{{ url_for('dashboard.setup_dashboard') }}" class="quick-access-card setup">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-cog" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Setup</span>
                </div>
            </a>

            <!-- Staff Card (Coming 1st June) -->
            <div class="quick-access-card staff coming-soon-card" style="position: relative; opacity: 0.7; cursor: not-allowed;">
                <div class="coming-soon-badge" style="position: absolute; top: 5px; right: 5px; background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 2px 6px; border-radius: 8px; font-size: 0.6rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); z-index: 10;">Coming 1st June</div>
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-users-cog" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Staff</span>
                </div>
            </div>

            <a href="{{ url_for('ticket.tickets') }}" class="quick-access-card support-tickets">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px; position: relative;">
                    <i class="fas fa-ticket-alt" style="font-size: 28px;"></i>
                    {% if open_tickets_count > 0 %}
                    <div class="ticket-badge" style="position: absolute; top: -5px; right: -5px; background-color: var(--primary-color); color: white; border-radius: 50%; width: 22px; height: 22px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                        {{ open_tickets_count }}
                    </div>
                    {% endif %}
                </div>
                <div class="quick-access-card-content">
                    <span>Support Tickets</span>
                </div>
            </a>

            <!-- Syncing Card (Coming 1st June) -->
            <div class="quick-access-card syncing coming-soon-card" style="position: relative; opacity: 0.7; cursor: not-allowed;">
                <div class="coming-soon-badge" style="position: absolute; top: 5px; right: 5px; background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 2px 6px; border-radius: 8px; font-size: 0.6rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); z-index: 10;">Coming 1st June</div>
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-sync-alt" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Syncing</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Sales Section -->
    <div class="top-sales-section" style="margin-top: 20px;">
        <div class="row">
            <!-- Full width Top 10 Sales -->
            <div class="col-12">
                <div class="card" style="background: linear-gradient(135deg, #1a1a2e, #16213e); border: 1px solid rgba(255, 255, 255, 0.05); border-radius: 12px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);">
                    <div class="card-header" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02)); border-bottom: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px 12px 0 0;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <h5 class="mb-0 me-3" style="color: #ffffff; font-weight: 600;">
                                    <i class="fas fa-fire me-2" style="color: #ff4081;"></i>Top 10 TCGPlayer Sales (Last 24 Hours)
                                </h5>
                                <div class="tcgplayer-badge" style="background: linear-gradient(45deg, #0066cc, #004499); color: white; padding: 4px 8px; border-radius: 6px; font-size: 0.75rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                                    <i class="fas fa-external-link-alt me-1" style="font-size: 0.7rem;"></i>TCGPlayer Data
                                </div>
                            </div>
                            <div class="game-filter-container">
                                <select id="gameFilter" class="form-select form-select-sm" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff; min-width: 150px;">
                                    <option value="">All Games</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <div id="topSalesContainer">
                            {% if top_sales_data and top_sales_data|length > 0 %}
                            <div class="sleek-table-container">
                                <table class="sleek-sales-table">
                                    <thead>
                                        <tr>
                                            <th class="rank-col">#</th>
                                            <th class="product-col">Product</th>
                                            <th class="expansion-col">Set</th>
                                            <th class="game-col">Game</th>
                                            <th class="variant-col">Variant</th>
                                            <th class="price-range-col">Price Range</th>
                                            <th class="avg-price-col">Avg Price</th>
                                            <th class="trend-col">Trend</th>
                                            <th class="last-sale-col">Last Sale</th>
                                            <th class="volume-col">Total Sales</th>
                                            <th class="inventory-col">Your Inventory</th>
                                            <th class="your-price-col">Your Price</th>
                                        </tr>
                                    </thead>
                                    <tbody id="topSalesTableBody">
                                        {% for item in top_sales_data %}
                                        <tr class="sales-row {% if not item.isFirstVariant %}variant-row{% endif %}" data-rank="{{ item.rank }}">
                                            <td class="rank-cell">
                                                {% if item.isFirstVariant %}
                                                    <div class="rank-badge rank-{{ item.rank }}">{{ item.rank }}</div>
                                                {% endif %}
                                            </td>
                                            <td class="product-cell">
                                                {% if item.isFirstVariant %}
                                                    <div class="product-info">
                                                        <div class="product-name" title="{{ item.name }}">{{ item.name }}</div>
                                                        <div class="product-id">ID: {{ item.productId }}</div>
                                                    </div>
                                                {% endif %}
                                            </td>
                                            <td class="expansion-cell">
                                                {% if item.isFirstVariant %}
                                                    <span class="expansion-tag">{{ item.expansionName or 'N/A' }}</span>
                                                {% endif %}
                                            </td>
                                            <td class="game-cell">
                                                {% if item.isFirstVariant %}
                                                    <span class="game-tag" data-game="{{ item.gameName or 'N/A' }}">{{ item.gameName or 'N/A' }}</span>
                                                {% endif %}
                                            </td>
                                            <td class="variant-cell">
                                                <div class="variant-display">
                                                    <span class="variant-tag">{{ item.variantName }}</span>
                                                </div>
                                            </td>
                                            <td class="price-range-cell">
                                                <div class="price-range-display">
                                                    {% if item.lowestPrice > 0 %}
                                                        <span class="price-low">{{ item.currencySymbol }}{{ "%.2f"|format(item.lowestPrice) }}</span>
                                                        <span class="price-separator">-</span>
                                                        <span class="price-high">{{ item.currencySymbol }}{{ "%.2f"|format(item.highestPrice) }}</span>
                                                    {% else %}
                                                        <span class="price-na">N/A</span>
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td class="avg-price-cell">
                                                <div class="avg-price-display">
                                                    {% if item.averagePrice > 0 %}
                                                        <span class="avg-price">{{ item.currencySymbol }}{{ "%.2f"|format(item.averagePrice) }}</span>
                                                    {% else %}
                                                        <span class="price-na">N/A</span>
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td class="trend-cell">
                                                <div class="trend-display">
                                                    {% if item.priceTrend %}
                                                        {% set trend = item.priceTrend %}
                                                        {% set change = item.percentChange %}
                                                        {% if trend == 'increasing' %}
                                                            <span class="trend-up">
                                                                <i class="fas fa-arrow-up"></i> +{{ "%.1f"|format(change) }}%
                                                            </span>
                                                        {% elif trend == 'decreasing' %}
                                                            <span class="trend-down">
                                                                <i class="fas fa-arrow-down"></i> {{ "%.1f"|format(change) }}%
                                                            </span>
                                                        {% else %}
                                                            <span class="trend-stable">
                                                                <i class="fas fa-minus"></i> Stable
                                                            </span>
                                                        {% endif %}
                                                    {% else %}
                                                        <span class="trend-stable">
                                                            <i class="fas fa-minus"></i> Stable
                                                        </span>
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td class="last-sale-cell">
                                                <div class="last-sale-display">
                                                    {% if item.lastSaleDate and item.lastSalePrice > 0 %}
                                                        <div class="last-sale-price">{{ item.currencySymbol }}{{ "%.2f"|format(item.lastSalePrice) }}</div>
                                                        <div class="last-sale-date">{{ item.lastSaleDate[:10] if item.lastSaleDate else 'N/A' }}</div>
                                                    {% else %}
                                                        <span class="price-na">No recent sales</span>
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td class="volume-cell">
                                                {% if item.isFirstVariant %}
                                                    <div class="volume-display">
                                                        <span class="volume-number">{{ item.volume }}</span>
                                                        <span class="volume-label">units</span>
                                                    </div>
                                                {% endif %}
                                            </td>
                                            <td class="inventory-cell">
                                                {% if item.isFirstVariant %}
                                                    <div class="inventory-display">
                                                        {% if item.userInventory > 0 %}
                                                            <span class="inventory-number">{{ item.userInventory }}</span>
                                                            <span class="inventory-label">in stock</span>
                                                        {% else %}
                                                            <span class="inventory-na">N/A</span>
                                                        {% endif %}
                                                    </div>
                                                {% endif %}
                                            </td>
                                            <td class="your-price-cell">
                                                {% if item.isFirstVariant %}
                                                    <div class="your-price-display">
                                                        {% if item.userPrice %}
                                                            <span class="your-price">{{ item.currencySymbol }}{{ "%.2f"|format(item.userPrice|float) }}</span>
                                                        {% else %}
                                                            <span class="price-na">N/A</span>
                                                        {% endif %}
                                                    </div>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center" style="padding: 2rem; color: rgba(255, 255, 255, 0.7);">
                                <i class="fas fa-chart-line fa-3x mb-3" style="color: rgba(107, 33, 168, 0.5);"></i>
                                <p class="mb-0">No TCGPlayer sales data available for the last 24 hours</p>
                                <small class="text-muted">Data is sourced from TCGPlayer marketplace</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional content can be added here in the future -->

</div>

<!-- Include favorites.js for handling favorites functionality -->
<script src="{{ url_for('static', filename='js/favorites.js') }}"></script>

<!-- Modal Elements -->
<!-- Premium Order Modal -->
<div class="modal fade" id="premiumOrderModal" tabindex="-1" aria-labelledby="premiumOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="premiumOrderModalLabel">Confirm Premium Order</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h4 id="premiumProductName"></h4>
                <p id="premiumProductDescription"></p>
                <p>Price: <strong id="premiumProductPrice"></strong></p>
                <div class="form-check mt-3">
                    <input class="form-check-input" type="checkbox" id="termsAgreement">
                    <label class="form-check-label" for="termsAgreement">
                        I agree to the terms and conditions
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmPremiumOrder" disabled>Confirm Order</button>
            </div>
        </div>
    </div>
</div>

<!-- Free Design Modal -->
<div class="modal fade" id="freeDesignModal" tabindex="-1" aria-labelledby="freeDesignModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="freeDesignModalLabel">Request Free Design</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="freeDesignForm">
                    <div class="mb-3">
                        <label for="designDetails" class="form-label">Design Details</label>
                        <textarea class="form-control" id="designDetails" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Premium Entitlements</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="logo_design" name="free_entitlements" id="logoDesign">
                            <label class="form-check-label" for="logoDesign">Logo Design</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="banner_design" name="free_entitlements" id="bannerDesign">
                            <label class="form-check-label" for="bannerDesign">Banner Design</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="colorPreferences" class="form-label">Color Preferences</label>
                        <input type="text" class="form-control" id="colorPreferences">
                    </div>
                    <div class="mb-3">
                        <label for="layoutPreferences" class="form-label">Layout Preferences</label>
                        <input type="text" class="form-control" id="layoutPreferences">
                    </div>
                    <div class="mb-3">
                        <label for="additionalNotes" class="form-label">Additional Notes</label>
                        <textarea class="form-control" id="additionalNotes" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Selected Designs</label>
                        <div id="selectedDesignsDisplay">
                            <div class="alert alert-warning">Please select at least one design option from the Premium Entitlements section.</div>
                        </div>
                        <input type="hidden" id="selectedDesignsInput">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitFreeDesignBtn" disabled>Submit Request</button>
            </div>
        </div>
    </div>
</div>

<!-- Request Success Modal -->
<div class="modal fade" id="requestSuccessModal" tabindex="-1" aria-labelledby="requestSuccessModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="requestSuccessModalLabel">Request Submitted</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <i class="fas fa-check-circle text-success" style="font-size: 48px;"></i>
                    <h4 class="mt-3">Request Submitted Successfully!</h4>
                    <p>Your request has been submitted and our team will review it shortly.</p>
                    <div id="ticket-link-container" class="mt-3"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Done For You Modal -->
<div class="modal fade" id="doneForYouModal" tabindex="-1" aria-labelledby="doneForYouModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="doneForYouModalLabel">Done For You Service Request</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="doneForYouForm">
                    <div class="mb-3">
                        <label for="doneForYouTitle" class="form-label">Request Title</label>
                        <input type="text" class="form-control" id="doneForYouTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="doneForYouDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="doneForYouDescription" rows="4" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="doneForYouUrgency" class="form-label">Urgency</label>
                        <select class="form-select" id="doneForYouUrgency">
                            <option value="low">Low - No rush</option>
                            <option value="medium" selected>Medium - Complete within a week</option>
                            <option value="high">High - Urgent, need ASAP</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitDoneForYouRequest">Submit Request</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {


        // Free Design Form Handling
        const freeDesignCheckboxes = document.querySelectorAll('input[name="free_entitlements"]');
        const selectedDesignsDisplay = document.getElementById('selectedDesignsDisplay');
        const selectedDesignsInput = document.getElementById('selectedDesignsInput');
        const submitFreeDesignBtn = document.getElementById('submitFreeDesignBtn');

        // Handle free design checkbox changes
        freeDesignCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedDesigns);
        });

        // Update selected designs display
        function updateSelectedDesigns() {
            const selectedDesigns = [];
            let displayHTML = '';

            freeDesignCheckboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedDesigns.push(checkbox.value);
                    const designName = checkbox.value.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
                    displayHTML += `<div class="badge bg-primary me-2 mb-2 p-2">${designName}</div>`;
                }
            });

            if (selectedDesigns.length > 0) {
                selectedDesignsDisplay.innerHTML = displayHTML;
                selectedDesignsInput.value = JSON.stringify(selectedDesigns);
                submitFreeDesignBtn.disabled = false;
            } else {
                selectedDesignsDisplay.innerHTML = '<div class="alert alert-warning">Please select at least one design option from the Premium Entitlements section.</div>';
                selectedDesignsInput.value = '';
                submitFreeDesignBtn.disabled = true;
            }
        }

        // Handle free design form submission
        submitFreeDesignBtn?.addEventListener('click', function() {
            const form = document.getElementById('freeDesignForm');
            const designDetails = document.getElementById('designDetails').value;
            const selectedDesigns = JSON.parse(selectedDesignsInput.value || '[]');

            if (!designDetails || selectedDesigns.length === 0) {
                showToast('Please select at least one design option and provide design details', 'error');
                return;
            }

            // Create request data
            const requestData = {
                selected_designs: selectedDesigns,
                design_details: designDetails,
                color_preferences: document.getElementById('colorPreferences').value,
                layout_preferences: document.getElementById('layoutPreferences').value,
                additional_notes: document.getElementById('additionalNotes').value,
                username: '{{ current_user.username }}',
                email: '{{ current_user.email }}'
            };

            // Send data to server
            fetch('/api/free-design-request', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close the current modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('freeDesignModal'));
                    modal.hide();

                    // Show success toast
                    showToast('Your free design request has been submitted successfully!', 'success');

                    // Reset form and checkboxes
                    form.reset();
                    freeDesignCheckboxes.forEach(checkbox => {
                        checkbox.checked = false;
                    });
                    updateSelectedDesigns();
                } else {
                    showToast(data.message || 'There was an error submitting your request', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('There was an error submitting your request. Please try again.', 'error');
            });
        });

        // Enterprise Feature Request Form Handling
        const submitRequestButtons = document.querySelectorAll('.submit-request-btn');

        // Premium Order Button Handling
        const orderPremiumButtons = document.querySelectorAll('.order-premium-btn');
        const premiumOrderModal = new bootstrap.Modal(document.getElementById('premiumOrderModal'));
        const confirmPremiumOrderBtn = document.getElementById('confirmPremiumOrder');
        const termsAgreementCheckbox = document.getElementById('termsAgreement');

        // Product details mapping
        const productDetails = {
            'shopify-theme': {
                name: 'Custom Shopify Theme',
                description: 'A professionally designed custom Shopify theme tailored specifically for your store.',
                price: '£999'
            }
        };

        // Handle premium order button clicks
        orderPremiumButtons.forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.getAttribute('data-product');
                const productPrice = this.getAttribute('data-price');
                const product = productDetails[productId];

                // Update modal with product details
                document.getElementById('premiumProductName').textContent = product.name;
                document.getElementById('premiumProductDescription').textContent = product.description;
                document.getElementById('premiumProductPrice').textContent = `£${productPrice}`;

                // Store product data for later use
                confirmPremiumOrderBtn.setAttribute('data-product', productId);
                confirmPremiumOrderBtn.setAttribute('data-price', productPrice);

                // Reset checkbox
                termsAgreementCheckbox.checked = false;
                confirmPremiumOrderBtn.disabled = true;

                // Show modal
                premiumOrderModal.show();
            });
        });

        // Handle terms agreement checkbox
        termsAgreementCheckbox.addEventListener('change', function() {
            confirmPremiumOrderBtn.disabled = !this.checked;
        });

        // Handle confirm order button
        confirmPremiumOrderBtn.addEventListener('click', function() {
            const productId = this.getAttribute('data-product');
            const productPrice = this.getAttribute('data-price');
            const product = productDetails[productId];

            // Create order data
            const orderData = {
                product_id: productId,
                product_name: product.name,
                price: productPrice,
                username: '{{ current_user.username }}',
                email: '{{ current_user.email }}',
                order_time: new Date().toISOString()
            };

            // Send order data to server
            fetch('/api/premium/order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(orderData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.payment_url) {
                    // Redirect to payment URL
                    window.location.href = data.payment_url;
                } else {
                    showToast('There was an error processing your order. Please try again.', 'error');
                    premiumOrderModal.hide();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('There was an error processing your order. Please try again.', 'error');
                premiumOrderModal.hide();
            });
        });

        // Fix select dropdown styling for dark mode
        const darkModeSelects = document.querySelectorAll('.enterprise-modal .form-select');
        darkModeSelects.forEach(select => {
            // Add a class to the parent modal when the select is clicked
            select.addEventListener('mousedown', function() {
                // Add a class to the body to style the dropdown via CSS
                document.body.classList.add('enterprise-dropdown-open');

                // Remove the class when the dropdown closes
                setTimeout(() => {
                    const checkForClose = setInterval(() => {
                        if (document.activeElement !== select) {
                            document.body.classList.remove('enterprise-dropdown-open');
                            clearInterval(checkForClose);
                        }
                    }, 100);

                    // Cleanup after 5 seconds regardless
                    setTimeout(() => {
                        document.body.classList.remove('enterprise-dropdown-open');
                        clearInterval(checkForClose);
                    }, 5000);
                }, 100);
            });
        });

        submitRequestButtons.forEach(button => {
            button.addEventListener('click', function() {
                const formId = this.getAttribute('data-form');
                const form = document.getElementById(formId);

                if (form && form.checkValidity()) {
                    // Get form data
                    const formData = new FormData(form);
                    const requestData = {};

                    // Add user information
                    requestData.username = '{{ current_user.username }}';
                    requestData.email = '{{ current_user.email }}';
                    requestData.request_time = new Date().toISOString();

                    // Add form data
                    for (const [key, value] of formData.entries()) {
                        requestData[key] = value;
                    }

                    // Convert to JSON
                    const jsonData = JSON.stringify(requestData);

                    // Send data to server
                    fetch('/enterprise/submit_request', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: jsonData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Close the current modal
                        const currentModal = bootstrap.Modal.getInstance(document.querySelector(`#${formId}`).closest('.modal'));
                        currentModal.hide();

                        // If we have a ticket ID, add it to the success message
                        if (data.ticket_id) {
                            const ticketLinkContainer = document.getElementById('ticket-link-container');
                            ticketLinkContainer.innerHTML = `You can track the status of your request in the <a href="{{ url_for('ticket.tickets') }}" class="text-primary">Support Tickets</a> section or <a href="{{ url_for('ticket.view_ticket', ticket_id='TICKET_ID_PLACEHOLDER') }}" class="text-primary">view this ticket directly</a>.`.replace('TICKET_ID_PLACEHOLDER', data.ticket_id);
                        }

                        // Show success modal
                        const successModal = new bootstrap.Modal(document.getElementById('requestSuccessModal'));
                        successModal.show();

                        // Reset form
                        form.reset();
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('There was an error submitting your request. Please try again.');
                    });
                } else {
                    // Trigger browser's form validation
                    form.reportValidity();
                }
            });
        });

        // Initialize quick access section functionality
        initQuickAccessSection();

        // Disable hover effects for coming soon cards
        const comingSoonCards = document.querySelectorAll('.coming-soon-card');
        comingSoonCards.forEach(card => {
            card.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            });
        });
    });

    function initQuickAccessSection() {

        // Add hover effects with animations
        const quickAccessCards = document.querySelectorAll('.quick-access-card');
        quickAccessCards.forEach(card => {
            // Add mouseenter event for subtle animation
            card.addEventListener('mouseenter', function() {
                const icon = this.querySelector('.quick-access-card-icon');
                icon.style.transition = 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            });

            // Add click effect
            card.addEventListener('click', function(e) {
                // Create ripple effect
                const ripple = document.createElement('div');
                ripple.classList.add('ripple-effect');

                // Position the ripple at click coordinates
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                ripple.style.left = `${x}px`;
                ripple.style.top = `${y}px`;

                // Add ripple to card
                this.appendChild(ripple);

                // Remove ripple after animation completes
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }

    // Handle Done For You Service form submission
    document.getElementById('submitDoneForYouRequest')?.addEventListener('click', function() {
        const form = document.getElementById('doneForYouForm');
        const title = document.getElementById('doneForYouTitle').value;
        const description = document.getElementById('doneForYouDescription').value;
        const urgency = document.getElementById('doneForYouUrgency').value;

        if (!title || !description) {
            showToast('Please fill in all required fields', 'error');
            return;
        }

        // Create request data
        const requestData = {
            request_type: 'done_for_you',
            username: '{{ current_user.username }}',
            email: '{{ current_user.email }}',
            title: title,
            description: description,
            urgency: urgency
        };

        // Send data to server to create a ticket
        fetch('/enterprise/submit_request', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            // Close the current modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('doneForYouModal'));
            modal.hide();

            // If we have a ticket ID, add it to the success message
            if (data.ticket_id) {
                const ticketLinkContainer = document.getElementById('ticket-link-container');
                ticketLinkContainer.innerHTML = `You can track the status of your request in the <a href="{{ url_for('ticket.tickets') }}" class="text-primary">Support Tickets</a> section or <a href="{{ url_for('ticket.view_ticket', ticket_id='TICKET_ID_PLACEHOLDER') }}" class="text-primary">view this ticket directly</a>.`.replace('TICKET_ID_PLACEHOLDER', data.ticket_id);
            }

            // Show success modal
            const successModal = new bootstrap.Modal(document.getElementById('requestSuccessModal'));
            successModal.show();

            // Reset form
            form.reset();
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('There was an error submitting your request. Please try again.', 'error');
        });
    });

    // Toast notification function
    function showToast(message, type = 'info') {
        // Create toast container if it doesn't exist
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.classList.add('toast-container');
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.classList.add('toast', `toast-${type}`);
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas ${type === 'info' ? 'fa-info-circle' : type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="toast-close"><i class="fas fa-times"></i></button>
        `;

        // Add toast to container
        toastContainer.appendChild(toast);

        // Add close button functionality
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', function() {
            toast.classList.add('toast-hiding');
            setTimeout(() => {
                toast.remove();
            }, 300);
        });

        // Auto-remove toast after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.classList.add('toast-hiding');
                setTimeout(() => {
                    if (toast.parentNode) toast.remove();
                }, 300);
            }
        }, 5000);

        // Animate toast in
        setTimeout(() => {
            toast.classList.add('toast-visible');
        }, 10);
    }
</script>

<style>
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0) translateX(-50%); }
        40% { transform: translateY(-5px) translateX(-50%); }
        60% { transform: translateY(-3px) translateX(-50%); }
    }

    /* Coming Soon Card Styling */
    .coming-soon-card {
        pointer-events: none !important;
        position: relative;
    }

    .coming-soon-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.1);
        border-radius: inherit;
        z-index: 1;
    }

    .coming-soon-card .quick-access-card-icon,
    .coming-soon-card .quick-access-card-content {
        position: relative;
        z-index: 2;
    }

    .coming-soon-badge {
        position: absolute;
        top: 5px;
        right: 5px;
        background: linear-gradient(45deg, #f39c12, #e67e22);
        color: white;
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 0.6rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        z-index: 10;
    }

    /* Prevent hover effects on coming soon cards */
    .coming-soon-card:hover {
        transform: none !important;
        box-shadow: none !important;
        background-color: inherit !important;
    }

    /* Sleek Table Styles */
    .sleek-table-container {
        overflow: hidden;
        border-radius: 0 0 12px 12px;
    }

    .sleek-sales-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: linear-gradient(135deg, #1a1a2e, #16213e);
        margin: 0;
    }

    .sleek-sales-table thead tr {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    }

    .sleek-sales-table th {
        padding: 1rem 1.25rem;
        text-align: left;
        font-weight: 700;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #ffffff;
        border: none;
        position: relative;
    }

    .sleek-sales-table th:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 25%;
        height: 50%;
        width: 1px;
        background: rgba(255, 255, 255, 0.1);
    }

    .sales-row {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        position: relative;
    }

    .sales-row:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
        transform: translateX(4px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .sales-row:last-child {
        border-bottom: none;
    }

    .sleek-sales-table td {
        padding: 1rem 1.25rem;
        border: none;
        vertical-align: middle;
    }

    .rank-cell {
        width: 60px;
        text-align: center;
    }

    .expansion-cell, .expansion-col {
        width: 20%;
        min-width: 150px;
    }

    .game-cell, .game-col {
        width: 20%;
        min-width: 120px;
    }

    .product-col {
        width: 20%;
        min-width: 200px;
    }

    .variant-col {
        width: 8%;
        min-width: 80px;
    }

    .variant-sales-col {
        width: 8%;
        min-width: 80px;
    }

    .price-range-col {
        width: 10%;
        min-width: 100px;
    }

    .avg-price-col {
        width: 8%;
        min-width: 80px;
    }

    .trend-col {
        width: 8%;
        min-width: 80px;
    }

    .last-sale-col {
        width: 10%;
        min-width: 100px;
    }

    .volume-col {
        width: 8%;
        min-width: 80px;
    }

    .rank-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        font-weight: 700;
        font-size: 0.85rem;
        color: white;
        background: linear-gradient(135deg, #6b21a8 0%, #9333ea 100%);
        box-shadow: 0 2px 8px rgba(107, 33, 168, 0.3);
        transition: all 0.3s ease;
    }

    .rank-badge.rank-1 {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #1a1a1a;
        box-shadow: 0 2px 12px rgba(255, 215, 0, 0.4);
    }

    .rank-badge.rank-2 {
        background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
        color: #1a1a1a;
        box-shadow: 0 2px 12px rgba(192, 192, 192, 0.4);
    }

    .rank-badge.rank-3 {
        background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
        color: white;
        box-shadow: 0 2px 12px rgba(205, 127, 50, 0.4);
    }

    .sales-row:hover .rank-badge {
        transform: scale(1.1);
        box-shadow: 0 4px 16px rgba(107, 33, 168, 0.4);
    }

    .product-cell {
        min-width: 300px;
        width: 35%;
    }

    .product-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .product-name {
        font-weight: 600;
        font-size: 0.9rem;
        color: #ffffff;
        line-height: 1.3;
        word-wrap: break-word;
        white-space: normal;
    }

    .product-id {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.6);
        font-weight: 500;
    }

    .expansion-tag, .game-tag {
        display: inline-block;
        padding: 0.375rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-align: center;
        word-wrap: break-word;
        white-space: normal;
        line-height: 1.2;
    }

    .expansion-tag {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(99, 102, 241, 0.2) 100%);
        color: #93c5fd;
        border: 1px solid rgba(59, 130, 246, 0.3);
    }

    .game-tag {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.2) 100%);
        color: #6ee7b7;
        border: 1px solid rgba(16, 185, 129, 0.3);
    }

    .volume-cell {
        text-align: center;
        min-width: 100px;
        width: 15%;
    }

    .volume-display {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.125rem;
    }

    .volume-number {
        font-size: 1.1rem;
        font-weight: 700;
        color: #6b21a8;
        background: linear-gradient(135deg, rgba(107, 33, 168, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        border: 1px solid rgba(107, 33, 168, 0.2);
        min-width: 40px;
        text-align: center;
    }

    .volume-label {
        font-size: 0.65rem;
        color: rgba(255, 255, 255, 0.5);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .sleek-sales-table th,
        .sleek-sales-table td {
            padding: 0.75rem 0.5rem;
        }

        .product-name {
            word-wrap: break-word;
            white-space: normal;
        }

        .expansion-tag, .game-tag {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
            word-wrap: break-word;
            white-space: normal;
        }

        .rank-badge {
            width: 28px;
            height: 28px;
            font-size: 0.8rem;
        }
    }

    /* Game filter dropdown styling */
    #gameFilter {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: #ffffff !important;
        border-radius: 6px;
    }

    #gameFilter:focus {
        background: rgba(255, 255, 255, 0.15) !important;
        border-color: rgba(107, 33, 168, 0.5) !important;
        box-shadow: 0 0 0 0.2rem rgba(107, 33, 168, 0.25) !important;
        color: #ffffff !important;
    }

    #gameFilter option {
        background: #1a1a2e !important;
        color: #ffffff !important;
    }

    /* Variant styling */
    .variant-tag {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 152, 0, 0.2) 100%);
        color: #ffc107;
        border: 1px solid rgba(255, 193, 7, 0.3);
        margin: 0.125rem;
    }

    /* Price styling */
    .price-range-display {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.25rem;
        font-size: 0.8rem;
    }

    .price-low {
        color: #10b981;
        font-weight: 600;
    }

    .price-high {
        color: #ef4444;
        font-weight: 600;
    }

    .price-separator {
        color: rgba(255, 255, 255, 0.5);
    }

    .avg-price {
        color: #3b82f6;
        font-weight: 700;
        font-size: 0.9rem;
    }

    .price-na {
        color: rgba(255, 255, 255, 0.4);
        font-style: italic;
    }

    /* Trend styling */
    .trend-display {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .trend-up {
        color: #10b981;
    }

    .trend-down {
        color: #ef4444;
    }

    .trend-stable {
        color: rgba(255, 255, 255, 0.6);
    }

    .trend-up i, .trend-down i, .trend-stable i {
        margin-right: 0.25rem;
        font-size: 0.7rem;
    }

    /* Cell styling */
    .variant-cell, .variant-sales-cell, .price-range-cell, .avg-price-cell, .trend-cell, .last-sale-cell {
        text-align: center;
        padding: 0.75rem 0.5rem;
    }

    /* Variant sales styling */
    .variant-sales-display {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 0.8rem;
    }

    .variant-sales-number {
        color: #fbbf24;
        font-weight: 700;
        font-size: 0.9rem;
    }

    .variant-sales-label {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.7rem;
    }

    /* Last sale styling */
    .last-sale-display {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 0.75rem;
        gap: 0.125rem;
    }

    .last-sale-price {
        color: #10b981;
        font-weight: 700;
        font-size: 0.8rem;
    }

    .last-sale-date {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.65rem;
    }

    /* Variant row styling */
    .variant-row {
        background: rgba(255, 255, 255, 0.02) !important;
        border-top: 1px solid rgba(255, 255, 255, 0.05) !important;
    }

    .variant-row td {
        border-top: 1px solid rgba(255, 255, 255, 0.05);
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    /* First variant row styling */
    .sales-row:not(.variant-row) {
        border-bottom: none;
    }

    /* Group styling for products with multiple variants */
    .sales-row:not(.variant-row) + .variant-row {
        border-top: none;
    }


</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const gameFilter = document.getElementById('gameFilter');
    const topSalesContainer = document.getElementById('topSalesContainer');

    // Load game names on page load
    loadGameNames();

    // Add event listener for game filter change
    gameFilter.addEventListener('change', function() {
        const selectedGame = this.value;
        loadTopSales(selectedGame);
    });

    function loadGameNames() {
        fetch('/api/dashboard/game-names')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    // Clear existing options except "All Games"
                    gameFilter.innerHTML = '<option value="">All Games</option>';

                    // Add game options
                    data.data.forEach(gameName => {
                        const option = document.createElement('option');
                        option.value = gameName;
                        option.textContent = gameName;
                        gameFilter.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading game names:', error);
            });
    }

    function loadTopSales(gameFilter = null) {
        // Show loading state
        topSalesContainer.innerHTML = `
            <div class="text-center" style="padding: 2rem; color: rgba(255, 255, 255, 0.7);">
                <i class="fas fa-spinner fa-spin fa-2x mb-3" style="color: rgba(107, 33, 168, 0.5);"></i>
                <p class="mb-0">Loading sales data...</p>
            </div>
        `;

        // Build URL with optional game filter
        let url = '/api/dashboard/top-sales';
        if (gameFilter) {
            url += `?game=${encodeURIComponent(gameFilter)}`;
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data && data.data.length > 0) {
                    renderTopSalesTable(data.data);
                } else {
                    showNoDataMessage(gameFilter);
                }
            })
            .catch(error => {
                console.error('Error loading top sales:', error);
                showErrorMessage();
            });
    }

    function renderTopSalesTable(salesData) {
        const tableHtml = `
            <div class="sleek-table-container">
                <table class="sleek-sales-table">
                    <thead>
                        <tr>
                                            <th class="rank-col">#</th>
                                            <th class="product-col">Product</th>
                                            <th class="expansion-col">Set</th>
                                            <th class="game-col">Game</th>
                                            <th class="variant-col">Variant</th>
                                            <th class="variant-sales-col">Variant Sales</th>
                                            <th class="price-range-col">Price Range</th>
                                            <th class="avg-price-col">Avg Price</th>
                                            <th class="trend-col">Trend</th>
                                            <th class="last-sale-col">Last Sale</th>
                                            <th class="volume-col">Total Sales</th>
                                            <th class="inventory-col">Your Inventory</th>
                                            <th class="your-price-col">Your Price</th>
                        </tr>
                    </thead>
                    <tbody id="topSalesTableBody">
                        ${salesData.map((item) => {
                            // Generate price range display
                            const priceRangeHtml = item.lowestPrice > 0
                                ? `<span class="price-low">${item.currencySymbol}${item.lowestPrice.toFixed(2)}</span>
                                   <span class="price-separator">-</span>
                                   <span class="price-high">${item.currencySymbol}${item.highestPrice.toFixed(2)}</span>`
                                : '<span class="price-na">N/A</span>';

                            // Generate average price display
                            const avgPriceHtml = item.averagePrice > 0
                                ? `<span class="avg-price">${item.currencySymbol}${item.averagePrice.toFixed(2)}</span>`
                                : '<span class="price-na">N/A</span>';

                            // Generate trend display
                            let trendHtml = '<span class="trend-stable"><i class="fas fa-minus"></i> Stable</span>';
                            if (item.priceTrend) {
                                if (item.priceTrend === 'increasing') {
                                    trendHtml = `<span class="trend-up"><i class="fas fa-arrow-up"></i> +${item.percentChange.toFixed(1)}%</span>`;
                                } else if (item.priceTrend === 'decreasing') {
                                    trendHtml = `<span class="trend-down"><i class="fas fa-arrow-down"></i> ${item.percentChange.toFixed(1)}%</span>`;
                                }
                            }

                            const variantRowClass = item.isFirstVariant ? 'sales-row' : 'sales-row variant-row';

                            return `
                                <tr class="${variantRowClass}" data-rank="${item.rank}">
                                    <td class="rank-cell">
                                        ${item.isFirstVariant ? `<div class="rank-badge rank-${item.rank}">${item.rank}</div>` : ''}
                                    </td>
                                    <td class="product-cell">
                                        ${item.isFirstVariant ? `
                                            <div class="product-info">
                                                <div class="product-name" title="${item.name || 'N/A'}">${item.name || 'N/A'}</div>
                                                <div class="product-id">ID: ${item.productId || 'N/A'}</div>
                                            </div>
                                        ` : ''}
                                    </td>
                                    <td class="expansion-cell">
                                        ${item.isFirstVariant ? `<span class="expansion-tag">${item.expansionName || 'N/A'}</span>` : ''}
                                    </td>
                                    <td class="game-cell">
                                            ${item.isFirstVariant ? `<span class="game-tag" data-game="${item.gameName || 'N/A'}">${item.gameName || 'N/A'}</span>` : ''}
                                    </td>
                                    <td class="variant-cell">
                                        <div class="variant-display">
                                            <span class="variant-tag">${item.variantName || 'Standard'}</span>
                                        </div>
                                    </td>
                                    <td class="variant-sales-cell">
                                        <div class="variant-sales-display">
                                            <span class="variant-sales-number">${item.variantVolume || 0}</span>
                                            <span class="variant-sales-label">units</span>
                                        </div>
                                    </td>
                                    <td class="price-range-cell">
                                        <div class="price-range-display">${priceRangeHtml}</div>
                                    </td>
                                    <td class="avg-price-cell">
                                        <div class="avg-price-display">${avgPriceHtml}</div>
                                    </td>
                                    <td class="trend-cell">
                                        <div class="trend-display">${trendHtml}</div>
                                    </td>
                                    <td class="last-sale-cell">
                                        <div class="last-sale-display">
                                            ${item.lastSaleDate && item.lastSalePrice > 0 ? `
                                                <div class="last-sale-price">${item.currencySymbol}${item.lastSalePrice.toFixed(2)}</div>
                                                <div class="last-sale-date">${item.lastSaleDate.substring(0, 10)}</div>
                                            ` : '<span class="price-na">No recent sales</span>'}
                                        </div>
                                    </td>
                                    <td class="volume-cell">
                                        ${item.isFirstVariant ? `
                                            <div class="volume-display">
                                                <span class="volume-number">${item.volume || 0}</span>
                                                <span class="volume-label">units</span>
                                            </div>
                                        ` : ''}
                                    </td>
                                    <td class="inventory-cell">
                                        ${item.isFirstVariant ? `
                                            <div class="inventory-display">
                                                ${item.userInventory > 0 ? `
                                                    <span class="inventory-number">${item.userInventory}</span>
                                                    <span class="inventory-label">in stock</span>
                                                ` : `
                                                    <span class="inventory-na">N/A</span>
                                                `}
                                            </div>
                                        ` : ''}
                                    </td>
                                    <td class="your-price-cell">
                                        ${item.isFirstVariant ? `
                                            <div class="your-price-display">
                                                ${item.userPrice ? `
                                                    <span class="your-price">${item.currencySymbol}${parseFloat(item.userPrice).toFixed(2)}</span>
                                                ` : `
                                                    <span class="price-na">N/A</span>
                                                `}
                                            </div>
                                        ` : ''}
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;

        topSalesContainer.innerHTML = tableHtml;
    }

    function showNoDataMessage(gameFilter) {
        const message = gameFilter
            ? `No sales data available for ${gameFilter} in the last 24 hours`
            : 'No sales data available for the last 24 hours';

        topSalesContainer.innerHTML = `
            <div class="text-center" style="padding: 2rem; color: rgba(255, 255, 255, 0.7);">
                <i class="fas fa-chart-line fa-3x mb-3" style="color: rgba(107, 33, 168, 0.5);"></i>
                <p class="mb-0">${message}</p>
            </div>
        `;
    }

    function showErrorMessage() {
        topSalesContainer.innerHTML = `
            <div class="text-center" style="padding: 2rem; color: rgba(255, 255, 255, 0.7);">
                <i class="fas fa-exclamation-triangle fa-3x mb-3" style="color: rgba(220, 53, 69, 0.5);"></i>
                <p class="mb-0">Error loading sales data. Please try again.</p>
            </div>
        `;
    }
});
</script>
{% endblock %}
