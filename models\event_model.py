from mongoengine import Document, StringField, IntField, FloatField, BooleanField, DateTimeField, DictField, ListField
from datetime import datetime

class Event(Document):
    # Basic Info
    title = StringField(required=True)
    description = StringField(required=True)
    game = StringField()  # Game name from collection or custom
    custom_game = BooleanField(default=False)  # Flag for custom events
    custom_game_name = StringField()  # Only used if custom_game is True
    
    # Dates and Recurrence
    start_datetime = DateTimeField(required=True)
    is_recurring = BooleanField(default=False)
    recurrence_type = StringField()  # weekly, monthly, custom
    recurrence_details = DictField()  # Flexible field for various recurrence patterns
    
    # For recurring events
    parent_event_id = StringField()  # Reference to the original event (for recurring instances)
    is_recurrence_parent = BooleanField(default=False)  # Flag for the original event
    recurrence_instances = ListField(StringField())  # IDs of child events (for parent event)
    
    # Capacity and tickets
    max_attendees = IntField(required=True)
    
    # Financial tracking
    ticket_price = FloatField(required=True)  # What customers pay
    cost_per_attendee = FloatField(default=0)  # Our cost per person
    profit_per_ticket = FloatField()  # Will be computed (ticket_price - cost_per_attendee)
    
    # Shopify integration
    shopify_product_id = StringField()
    shopify_variant_id = StringField()
    shopify_product_url = StringField()  # Store full URL for direct access to Shopify admin
    
    # Event details
    event_type = StringField(required=True)  # tournament, pre-release, draft, etc.
    prize_details = StringField()  # Description of prizes

    # Event Image
    event_image_url = StringField()  # URL to the event banner/image
    event_image_filename = StringField()  # Original filename for reference

    # Metadata
    created_by = StringField(required=True)  # Username
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    status = StringField(default="upcoming")  # upcoming, active, completed, canceled
    
    meta = {
        'collection': 'events',
        'indexes': [
            'title',
            'created_by',
            'start_datetime',
            'event_type',
            'status',
            'parent_event_id',
            'is_recurrence_parent'
        ]
    }
    
    def save(self, *args, **kwargs):
        # Update the updated_at field
        self.updated_at = datetime.utcnow()
        
        # Calculate profit per ticket if not set
        if self.ticket_price is not None and self.cost_per_attendee is not None:
            self.profit_per_ticket = self.ticket_price - self.cost_per_attendee
            
        return super(Event, self).save(*args, **kwargs)
