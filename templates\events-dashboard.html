{% extends "base.html" %}
{% block title %}Events Dashboard{% endblock %}
{% block content %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/events.css') }}">

<div class="container-fluid dashboard-container">
    {% if not current_user.subscription or current_user.subscription.name == 'Free' %}
    <div class="alert alert-primary mt-1 mb-2 py-2 d-flex justify-content-between align-items-center" role="alert" style="font-size: 0.9rem;">
        <div class="text-center w-100">
            <i class="fas fa-star me-1"></i>
            <strong>Upgrade your plan to unlock all features!</strong> Get access to advanced analytics, premium tools, and more.
        </div>
        <a href="{{ url_for('activation.activating') }}" class="btn btn-primary btn-sm py-1 px-2">
            Upgrade Now
        </a>
    </div>
    {% endif %}

    <!-- Events Dashboard Header -->
    <div class="setup-header">
        <h1><i class="fas fa-calendar-alt me-2"></i>Events Dashboard</h1>
        <p class="text-muted">Create and manage your events, track attendees, and view results</p>
    </div>
    <!-- Events Options Grid -->
    <div class="setup-grid">
        <!-- Create Events Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-plus-circle"></i>
                <h2>Create Events</h2>
            </div>
            <div class="setup-card-body">
                <p>Create and schedule new events. Set dates, locations, and event details.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Schedule one-time events</li>
                    <li><i class="fas fa-check-circle"></i> Create recurring events</li>
                    <li><i class="fas fa-check-circle"></i> Set ticket prices</li>
                    <li><i class="fas fa-check-circle"></i> Customize event details</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('events.create_events') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create Event
                </a>
            </div>
        </div>

        <!-- View Events Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-calendar-alt"></i>
                <h2>View Events</h2>
            </div>
            <div class="setup-card-body">
                <p>Browse and manage your current events. View details, participants, and event status.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> View all scheduled events</li>
                    <li><i class="fas fa-check-circle"></i> Edit event details</li>
                    <li><i class="fas fa-check-circle"></i> Cancel or reschedule</li>
                    <li><i class="fas fa-check-circle"></i> Track event status</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('events.view_events') }}" class="btn btn-primary">
                    <i class="fas fa-eye me-2"></i>View Events
                </a>
            </div>
        </div>

        <!-- Attendees Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-users"></i>
                <h2>Attendees</h2>
            </div>
            <div class="setup-card-body">
                <p>Manage event attendees. View registrations, check-ins, and participant information.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> View registered attendees</li>
                    <li><i class="fas fa-check-circle"></i> Check-in participants</li>
                    <li><i class="fas fa-check-circle"></i> Manage waitlists</li>
                    <li><i class="fas fa-check-circle"></i> Export attendee data</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('events.view_events') }}" class="btn btn-primary">
                    <i class="fas fa-user-check me-2"></i>Manage Attendees
                </a>
            </div>
        </div>

        <!-- Results Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-trophy"></i>
                <h2>Results</h2>
            </div>
            <div class="setup-card-body">
                <p>Track event results, winners, and tournament outcomes.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Record tournament results</li>
                    <li><i class="fas fa-check-circle"></i> Track player rankings</li>
                    <li><i class="fas fa-check-circle"></i> View historical results</li>
                    <li><i class="fas fa-check-circle"></i> Generate result reports</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('events.results') }}" class="btn btn-primary">
                    <i class="fas fa-list-ol me-2"></i>View Results
                </a>
            </div>
        </div>

        <!-- My URL Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-link"></i>
                <h2>My URL</h2>
            </div>
            <div class="setup-card-body">
                <p>Access your public events page that customers can view and register for events.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Share with customers</li>
                    <li><i class="fas fa-check-circle"></i> Online registration</li>
                    <li><i class="fas fa-check-circle"></i> Public event calendar</li>
                    <li><i class="fas fa-check-circle"></i> Custom branding</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="https://enterprise.tcgsync.com/events/{{ current_user.username }}" target="_blank" class="btn btn-primary">
                    <i class="fas fa-external-link-alt me-2"></i>View My URL
                </a>
            </div>
        </div>
    </div>

    <!-- Stats and Upcoming Events Row -->
    <div class="row mt-4">
        <!-- Stats Card -->
        <div class="col-lg-6 mb-4">
            <div class="stats-card">
                <div class="stats-card-header">
                    <div class="icon-wrapper">
                        <i class="fas fa-chart-bar" style="color: #3498db;"></i>
                    </div>
                    <h4 class="text-white mb-0">Event Statistics</h4>
                </div>
                <div class="stats-card-body">
                    <div class="row text-center">
                        <div class="col-6 col-md-3 mb-4">
                            <div class="stats-counter text-info">{{ stats.upcoming }}</div>
                            <div class="text-white-50">Upcoming</div>
                        </div>
                        <div class="col-6 col-md-3 mb-4">
                            <div class="stats-counter text-success">{{ stats.active }}</div>
                            <div class="text-white-50">Active</div>
                        </div>
                        <div class="col-6 col-md-3 mb-4">
                            <div class="stats-counter text-warning">{{ stats.total_attendees }}</div>
                            <div class="text-white-50">Attendees</div>
                        </div>
                        <div class="col-6 col-md-3 mb-4">
                            <div class="stats-counter text-danger">£{{ stats.total_revenue|round(2) }}</div>
                            <div class="text-white-50">Revenue</div>
                        </div>
                    </div>

                    {% if stats.upcoming == 0 and stats.active == 0 %}
                    <div class="text-center mt-3">
                        <p class="text-white-50">Event statistics will be displayed here once you create events.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Upcoming Events Card -->
        <div class="col-lg-6 mb-4">
            <div class="events-card">
                <div class="events-card-header">
                    <div class="d-flex align-items-center">
                        <div class="icon-wrapper">
                            <i class="fas fa-calendar-check" style="color: #2ecc71;"></i>
                        </div>
                        <h4 class="text-white mb-0">Upcoming Events</h4>
                    </div>
                    <a href="{{ url_for('events.view_events') }}" class="btn btn-sm btn-outline-light">View All</a>
                </div>
                <div class="events-card-body">
                    {% if upcoming_events and upcoming_events|length > 0 %}
                        {% for event in upcoming_events %}
                            <div class="upcoming-event">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h5 class="text-white mb-1">{{ event.title }}</h5>
                                        <div class="mb-2">
                                            <span class="badge badge-{{ event.event_type }}">
                                                {{ event.event_type|title }}
                                            </span>
                                            {% if event.is_recurring and event.is_recurrence_parent %}
                                                <span class="badge bg-info ms-1">Recurring</span>
                                            {% endif %}
                                        </div>
                                        <div class="text-white-50 small">
                                            <i class="far fa-calendar me-1"></i> {{ event.start_datetime.strftime('%d %b %Y') }}
                                            <i class="far fa-clock ms-2 me-1"></i> {{ event.start_datetime.strftime('%I:%M %p') }}
                                        </div>
                                        <div class="text-white-50 small mt-1">
                                            <i class="fas fa-users me-1"></i> {{ event.max_attendees }} attendees
                                            <i class="fas fa-tag ms-2 me-1"></i> £{{ event.ticket_price }}
                                        </div>
                                    </div>
                                    <a href="{{ url_for('events.view_events') }}" class="btn btn-sm btn-outline-light">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-calendar-alt"></i>
                            <h5>No Upcoming Events</h5>
                            <p>You don't have any upcoming events scheduled.</p>
                            <a href="{{ url_for('events.create_events') }}" class="btn btn-success mt-2">
                                <i class="fas fa-plus-circle me-2"></i>Create Event
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
