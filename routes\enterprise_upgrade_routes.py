import stripe
from flask import Blueprint, current_app, request, jsonify, render_template, redirect, url_for, flash
from flask_login import current_user, login_required
from models.user_model import User, Subscription
from datetime import datetime
from config import Config
import logging

logger = logging.getLogger(__name__)

enterprise_upgrade_bp = Blueprint('enterprise_upgrade', __name__)

stripe.api_key = Config.STRIPE_SECRET_KEY

@enterprise_upgrade_bp.route('/create-enterprise-payment-link', methods=['POST'])
@login_required
def create_enterprise_payment_link():
    """Create a payment link for the enterprise upgrade"""
    # Only lifetime subscribers can upgrade
    if not current_user.subscription or current_user.subscription.name != 'Lifetime':
        return jsonify({'error': 'Only lifetime subscribers can upgrade to enterprise'}), 403
    
    try:
        # Create a product for the enterprise upgrade
        product = stripe.Product.create(
            name='Enterprise Upgrade',
            description='Upgrade to Enterprise level while retaining lifetime status'
        )

        # Create a price for this product (£4,000)
        price = stripe.Price.create(
            product=product.id,
            unit_amount=400000,  # £4,000 in pence
            currency='gbp'
        )

        # Create the payment link
        payment_link = stripe.PaymentLink.create(
            line_items=[{
                'price': price.id,
                'quantity': 1
            }],
            mode='payment',
            metadata={
                'username': current_user.username,
                'upgrade_type': 'lifetime_to_enterprise'
            }
        )
        
        return jsonify({'payment_link': payment_link.url})
    except Exception as e:
        logger.error(f"Error creating enterprise upgrade payment link: {str(e)}")
        return jsonify({'error': str(e)}), 500

@enterprise_upgrade_bp.route('/process-enterprise-upgrade', methods=['POST'])
def process_enterprise_upgrade():
    """Webhook endpoint for processing enterprise upgrades after payment"""
    payload = request.get_data()
    sig_header = request.headers.get('Stripe-Signature')

    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, Config.STRIPE_WEBHOOK_SECRET
        )
    except ValueError as e:
        logger.error(f"Invalid payload: {str(e)}")
        return jsonify({'error': 'Invalid payload'}), 400
    except stripe.error.SignatureVerificationError as e:
        logger.error(f"Invalid signature: {str(e)}")
        return jsonify({'error': 'Invalid signature'}), 400

    # Handle the checkout.session.completed event
    if event['type'] == 'checkout.session.completed':
        session = event['data']['object']
        
        # Check if this is an enterprise upgrade
        if session.metadata.get('upgrade_type') == 'lifetime_to_enterprise':
            username = session.metadata.get('username')
            if not username:
                logger.error("No username found in session metadata")
                return jsonify({'error': 'No username found'}), 400
            
            try:
                # Find the user
                user = User.objects(username=username).first()
                if not user:
                    logger.error(f"User not found: {username}")
                    return jsonify({'error': 'User not found'}), 404
                
                # Update the user's subscription to Enterprise while keeping lifetime status
                if user.subscription and user.subscription.name == 'Lifetime':
                    # Create a new Enterprise subscription
                    enterprise_sub = Subscription(
                        name='Enterprise',
                        start_date=datetime.utcnow()
                    ).save()
                    
                    # Update the user's subscription
                    user.subscription = enterprise_sub
                    user.save()
                    
                    logger.info(f"User {username} upgraded from Lifetime to Enterprise")

                    return jsonify({'success': True})
                else:
                    logger.error(f"User {username} does not have a Lifetime subscription")
                    return jsonify({'error': 'User does not have a Lifetime subscription'}), 400
            except Exception as e:
                logger.error(f"Error processing enterprise upgrade: {str(e)}")
                return jsonify({'error': str(e)}), 500
    
    return jsonify({'received': True})
