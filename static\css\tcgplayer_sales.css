/* TCGPlayer Sales Dashboard Styles */

/* Dashboard Card styling */
.dashboard-card {
    border-radius: 12px;
    background-color: rgba(25, 25, 39, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* Card header styling */
.card-header {
    background-color: rgba(52, 152, 219, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* Icon wrapper styling */
.icon-wrapper {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background-color: rgba(52, 152, 219, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Stats counter */
.stats-counter {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: rgba(255, 255, 255, 0.7);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.3);
}

/* TCGPlayer Sales Table Styles */

/* Adjust column widths */
.sleek-sales-table .product-col,
.sleek-sales-table .product-cell {
    width: 15% !important; /* Reduced from 20% */
    min-width: 150px !important; /* Reduced from 200px */
}

.sleek-sales-table .expansion-col,
.sleek-sales-table .expansion-cell {
    width: 12% !important; /* Reduced from 20% */
    min-width: 100px !important; /* Reduced from 150px */
}

.sleek-sales-table .game-col,
.sleek-sales-table .game-cell {
    width: 10% !important; /* Reduced from 20% */
    min-width: 80px !important; /* Reduced from 120px */
}

/* Game tag color coding */
.game-tag[data-game="Magic: The Gathering"] {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.2) 0%, rgba(0, 83, 175, 0.2) 100%) !important;
    color: #4dabff !important;
    border: 1px solid rgba(0, 123, 255, 0.3) !important;
}

.game-tag[data-game="Pokemon"] {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 152, 0, 0.2) 100%) !important;
    color: #ffd43b !important;
    border: 1px solid rgba(255, 193, 7, 0.3) !important;
}

.game-tag[data-game="Yu-Gi-Oh!"] {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.2) 0%, rgba(170, 43, 59, 0.2) 100%) !important;
    color: #ff6b6b !important;
    border: 1px solid rgba(220, 53, 69, 0.3) !important;
}

.game-tag[data-game="Flesh and Blood"] {
    background: linear-gradient(135deg, rgba(111, 66, 193, 0.2) 0%, rgba(81, 46, 143, 0.2) 100%) !important;
    color: #b197fc !important;
    border: 1px solid rgba(111, 66, 193, 0.3) !important;
}

.game-tag[data-game="Digimon"] {
    background: linear-gradient(135deg, rgba(32, 201, 151, 0.2) 0%, rgba(22, 161, 111, 0.2) 100%) !important;
    color: #63e6be !important;
    border: 1px solid rgba(32, 201, 151, 0.3) !important;
}

.game-tag[data-game="One Piece"] {
    background: linear-gradient(135deg, rgba(0, 150, 136, 0.2) 0%, rgba(0, 110, 96, 0.2) 100%) !important;
    color: #40c4b7 !important;
    border: 1px solid rgba(0, 150, 136, 0.3) !important;
}

.game-tag[data-game="Dragon Ball Super"] {
    background: linear-gradient(135deg, rgba(255, 87, 34, 0.2) 0%, rgba(215, 67, 24, 0.2) 100%) !important;
    color: #ff8a65 !important;
    border: 1px solid rgba(255, 87, 34, 0.3) !important;
}

.game-tag[data-game="Lorcana"] {
    background: linear-gradient(135deg, rgba(156, 39, 176, 0.2) 0%, rgba(116, 29, 136, 0.2) 100%) !important;
    color: #ce93d8 !important;
    border: 1px solid rgba(156, 39, 176, 0.3) !important;
}

.game-tag[data-game="MetaZoo"] {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(56, 155, 60, 0.2) 100%) !important;
    color: #81c784 !important;
    border: 1px solid rgba(76, 175, 80, 0.3) !important;
}

/* Default style for any other games */
.game-tag:not([data-game="Magic: The Gathering"]):not([data-game="Pokemon"]):not([data-game="Yu-Gi-Oh!"]):not([data-game="Flesh and Blood"]):not([data-game="Digimon"]):not([data-game="One Piece"]):not([data-game="Dragon Ball Super"]):not([data-game="Lorcana"]):not([data-game="MetaZoo"]) {
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.2) 0%, rgba(88, 97, 105, 0.2) 100%) !important;
    color: #adb5bd !important;
    border: 1px solid rgba(108, 117, 125, 0.3) !important;
}

/* Your Inventory and Your Price column styles */
.inventory-col,
.your-price-col {
    width: 10% !important;
    min-width: 100px !important;
}

.inventory-cell,
.your-price-cell {
    text-align: center;
}

.inventory-display,
.your-price-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.125rem;
}

.inventory-number {
    font-size: 1rem;
    font-weight: 700;
    color: #10b981;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    border: 1px solid rgba(16, 185, 129, 0.2);
    min-width: 40px;
    text-align: center;
}

.inventory-label {
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.5);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.your-price {
    font-size: 1rem;
    font-weight: 700;
    color: #3b82f6;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    border: 1px solid rgba(59, 130, 246, 0.2);
    min-width: 40px;
    text-align: center;
}

.inventory-na {
    color: rgba(255, 255, 255, 0.4);
    font-style: italic;
    font-size: 0.8rem;
}
