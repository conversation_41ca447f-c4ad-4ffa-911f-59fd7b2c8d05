/* Setup Dashboard Styles */

/* Card color variations */
.setup-card:nth-child(1) .setup-card-header i {
    color: #3498db; /* Profile - Blue */
}

.setup-card:nth-child(2) .setup-card-header i {
    color: #9b59b6; /* Integrations - Purple */
}

.setup-card:nth-child(3) .setup-card-header i {
    color: #2ecc71; /* Pricing - Green */
}

.setup-card:nth-child(4) .setup-card-header i {
    color: #e67e22; /* Buylist - Orange */
}

/* Button color variations */
.setup-card:nth-child(1) .setup-card-footer .btn {
    background-color: #3498db;
    border-color: #3498db;
}

.setup-card:nth-child(2) .setup-card-footer .btn {
    background-color: #9b59b6;
    border-color: #9b59b6;
}

.setup-card:nth-child(3) .setup-card-footer .btn {
    background-color: #2ecc71;
    border-color: #2ecc71;
}

.setup-card:nth-child(4) .setup-card-footer .btn {
    background-color: #e67e22;
    border-color: #e67e22;
}

/* Hover effects for buttons */
.setup-card:nth-child(1) .setup-card-footer .btn:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.setup-card:nth-child(2) .setup-card-footer .btn:hover {
    background-color: #8e44ad;
    border-color: #8e44ad;
}

.setup-card:nth-child(3) .setup-card-footer .btn:hover {
    background-color: #27ae60;
    border-color: #27ae60;
}

.setup-card:nth-child(4) .setup-card-footer .btn:hover {
    background-color: #d35400;
    border-color: #d35400;
}

/* Subtle border glow effects on hover */
.setup-card:nth-child(1):hover {
    box-shadow: 0 8px 30px rgba(52, 152, 219, 0.2);
    border-color: rgba(52, 152, 219, 0.3);
}

.setup-card:nth-child(2):hover {
    box-shadow: 0 8px 30px rgba(155, 89, 182, 0.2);
    border-color: rgba(155, 89, 182, 0.3);
}

.setup-card:nth-child(3):hover {
    box-shadow: 0 8px 30px rgba(46, 204, 113, 0.2);
    border-color: rgba(46, 204, 113, 0.3);
}

.setup-card:nth-child(4):hover {
    box-shadow: 0 8px 30px rgba(230, 126, 34, 0.2);
    border-color: rgba(230, 126, 34, 0.3);
}

/* Animation for the check icons */
.setup-features-list li i {
    transition: transform 0.3s ease;
}

.setup-card:hover .setup-features-list li i {
    transform: scale(1.2);
}

/* Staggered animation for list items on hover */
.setup-card:hover .setup-features-list li:nth-child(1) i {
    transition-delay: 0.05s;
}

.setup-card:hover .setup-features-list li:nth-child(2) i {
    transition-delay: 0.1s;
}

.setup-card:hover .setup-features-list li:nth-child(3) i {
    transition-delay: 0.15s;
}

.setup-card:hover .setup-features-list li:nth-child(4) i {
    transition-delay: 0.2s;
}
