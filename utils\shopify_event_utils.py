import shopify
from models.user_model import User
from datetime import datetime

def get_shopify_credentials(username):
    """
    Get Shopify credentials for the given username

    Args:
        username (str): The username to get credentials for

    Returns:
        dict: Dictionary containing shop_url and access_token

    Raises:
        ValueError: If credentials are not found
    """
    user = User.objects(username=username).first()
    if not user:
        raise ValueError(f"User '{username}' not found in database")

    if not user.shopifyStoreName:
        raise ValueError(f"User '{username}' does not have a Shopify store name configured. Please configure your Shopify integration in profile settings.")

    if not user.shopifyAccessToken:
        raise ValueError(f"User '{username}' does not have a Shopify access token configured. Please configure your Shopify integration in profile settings.")

    return {
        'shop_url': f"{user.shopifyStoreName}.myshopify.com",
        'access_token': user.shopifyAccessToken
    }

def create_event_ticket_product(event, username):
    """
    Create a Shopify product for event tickets

    Args:
        event (Event): The event object
        username (str): The username of the user creating the event

    Returns:
        tuple: (success, product_id or error_message)
    """
    credentials = get_shopify_credentials(username)

    # Initialize Shopify API session
    session = shopify.Session(credentials['shop_url'], '2023-07', credentials['access_token'])
    shopify.ShopifyResource.activate_session(session)

    try:
        # Format date for product title
        date_str = event.start_datetime.strftime("%d %b %Y at %I:%M %p")

        # Create new product with variant information included
        product = shopify.Product()

        # Set title based on whether it's a recurring instance or not
        if event.parent_event_id:
            product.title = f"Ticket: {event.title} - {date_str}"
        else:
            product.title = f"Ticket: {event.title}"

            # Add date to title if it's a single event
            if not event.is_recurring:
                product.title += f" - {date_str}"

        # Set product description
        product.body_html = f"""
        <h3>{event.title}</h3>
        <p><strong>Date:</strong> {date_str}</p>
        <p><strong>Game:</strong> {event.custom_game_name if event.custom_game else event.game}</p>
        <p><strong>Event Type:</strong> {event.event_type.title()}</p>
        <p><strong>Maximum Attendees:</strong> {event.max_attendees}</p>
        <div>{event.description}</div>
        """

        if event.prize_details:
            product.body_html += f"<h4>Prize Details</h4><div>{event.prize_details}</div>"

        product.product_type = "Event Ticket"
        product.vendor = "TCGSync Events"

        # Add tags
        tags = [event.event_type]
        if not event.custom_game:
            tags.append(event.game)
        if event.parent_event_id:
            tags.append("recurring-event")
        product.tags = ", ".join(tags)

        # Create a variant with the product
        variant = shopify.Variant()
        variant.price = event.ticket_price
        variant.requires_shipping = False
        variant.inventory_management = "shopify"
        variant.title = "General Admission"

        # Add the variant to the product
        product.variants = [variant]

        # Save the product with the variant
        if not product.save():
            return False, product.errors.full_messages()

        # Add product image
        try:
            image = shopify.Image()
            image.product_id = product.id
            image.src = "https://dh97sxltltum5.cloudfront.net/uploads/product_id_811/files/tickets.png"
            image.save()
        except Exception as e:
            # Log the error but continue - image is not critical
            print(f"Error adding product image: {str(e)}")

        # Get the saved variant (should be the first one)
        if not product.variants or len(product.variants) == 0:
            return False, "No variants found after saving product"

        variant = product.variants[0]

        # Get the default location ID for inventory
        locations = shopify.Location.find()
        if not locations:
            return False, "No locations found for inventory tracking"

        location_id = locations[0].id

        # Get the inventory item ID for the variant
        inventory_item_id = variant.inventory_item_id

        # Set inventory level for the variant at the location
        try:
            # First, activate the inventory item to ensure it's trackable
            inventory_item = shopify.InventoryItem.find(inventory_item_id)
            if inventory_item:
                inventory_item.tracked = True
                inventory_item.save()

            # Set the inventory level
            inventory_level = shopify.InventoryLevel.set(
                location_id=location_id,
                inventory_item_id=inventory_item_id,
                available=event.max_attendees
            )

            if not inventory_level:
                return False, "Failed to set inventory level"
        except Exception as e:
            return False, f"Error setting inventory: {str(e)}"

        # Use the user's actual Shopify store domain for the frontend URL
        # Extract the store name from the shop_url (remove .myshopify.com)
        store_name = credentials['shop_url'].replace('.myshopify.com', '')
        product_frontend_url = f"https://{store_name}.myshopify.com/products/{product.handle}"

        return True, {
            'product_id': str(product.id),
            'variant_id': str(variant.id),
            'product_url': product_frontend_url
        }


    except Exception as e:
        return False, str(e)
    finally:
        shopify.ShopifyResource.clear_session()

def update_event_ticket_product(event, username):
    """
    Update an existing Shopify product for event tickets

    Args:
        event (Event): The event object with updated information
        username (str): The username of the user updating the event

    Returns:
        tuple: (success, result or error_message)
    """
    if not event.shopify_product_id:
        return False, "No Shopify product ID associated with this event"

    credentials = get_shopify_credentials(username)

    # Initialize Shopify API session
    session = shopify.Session(credentials['shop_url'], '2023-07', credentials['access_token'])
    shopify.ShopifyResource.activate_session(session)

    try:
        # Find the product
        product = shopify.Product.find(event.shopify_product_id)
        if not product:
            return False, "Product not found in Shopify"

        # Format date for product title
        date_str = event.start_datetime.strftime("%d %b %Y at %I:%M %p")

        # Update title based on whether it's a recurring instance or not
        if event.parent_event_id:
            product.title = f"Ticket: {event.title} - {date_str}"
        else:
            product.title = f"Ticket: {event.title}"

            # Add date to title if it's a single event
            if not event.is_recurring:
                product.title += f" - {date_str}"

        # Update product description
        product.body_html = f"""
        <h3>{event.title}</h3>
        <p><strong>Date:</strong> {date_str}</p>
        <p><strong>Game:</strong> {event.custom_game_name if event.custom_game else event.game}</p>
        <p><strong>Event Type:</strong> {event.event_type.title()}</p>
        <p><strong>Maximum Attendees:</strong> {event.max_attendees}</p>
        <div>{event.description}</div>
        """

        if event.prize_details:
            product.body_html += f"<h4>Prize Details</h4><div>{event.prize_details}</div>"

        # Update tags
        tags = [event.event_type]
        if not event.custom_game:
            tags.append(event.game)
        if event.parent_event_id:
            tags.append("recurring-event")
        product.tags = ", ".join(tags)

        # Save the updated product
        if not product.save():
            return False, product.errors.full_messages()

        # Update variant price
        if product.variants and len(product.variants) > 0:
            variant = product.variants[0]
            variant.price = event.ticket_price

            # Save the variant
            if not variant.save():
                return False, variant.errors.full_messages()

            # Update inventory level
            try:
                # Get the default location ID for inventory
                locations = shopify.Location.find()
                if not locations:
                    return False, "No locations found for inventory tracking"

                location_id = locations[0].id

                # Get the inventory item ID for the variant
                inventory_item_id = variant.inventory_item_id

                # Set inventory level for the variant at the location
                inventory_level = shopify.InventoryLevel.set(
                    location_id=location_id,
                    inventory_item_id=inventory_item_id,
                    available=event.max_attendees
                )

                if not inventory_level:
                    return False, "Failed to update inventory level"
            except Exception as e:
                return False, f"Error updating inventory: {str(e)}"

        # Use the user's actual Shopify store domain for the frontend URL
        product_handle = product.handle
        store_name = credentials['shop_url'].replace('.myshopify.com', '')
        product_frontend_url = f"https://{store_name}.myshopify.com/products/{product_handle}"

        return True, {
            'product_id': str(product.id),
            'variant_id': str(variant.id) if product.variants else None,
            'product_url': product_frontend_url
        }

    except Exception as e:
        return False, str(e)
    finally:
        shopify.ShopifyResource.clear_session()

def delete_event_ticket_product(event, username):
    """
    Delete a Shopify product for event tickets

    Args:
        event (Event): The event object to delete
        username (str): The username of the user deleting the event

    Returns:
        tuple: (success, result or error_message)
    """
    if not event.shopify_product_id:
        return False, "No Shopify product ID associated with this event"

    credentials = get_shopify_credentials(username)

    # Initialize Shopify API session
    session = shopify.Session(credentials['shop_url'], '2023-07', credentials['access_token'])
    shopify.ShopifyResource.activate_session(session)

    try:
        # Find the product
        product = shopify.Product.find(event.shopify_product_id)
        if not product:
            return False, "Product not found in Shopify"

        # Delete the product
        if not product.destroy():
            return False, "Failed to delete product"

        return True, "Product deleted successfully"

    except Exception as e:
        return False, str(e)
    finally:
        shopify.ShopifyResource.clear_session()

def generate_recurrence_dates(start_date, recurrence_type, recurrence_details):
    """
    Generate dates for recurring events

    Args:
        start_date (datetime): The start date of the first event
        recurrence_type (str): Type of recurrence (weekly, monthly, custom)
        recurrence_details (dict): Details of the recurrence pattern

    Returns:
        list: List of datetime objects for each occurrence
    """
    from dateutil.relativedelta import relativedelta
    import calendar

    dates = []

    if recurrence_type == 'custom':
        # For custom dates, they're already provided
        if 'dates' in recurrence_details:
            for date_str in recurrence_details['dates']:
                try:
                    # Parse the date string into a datetime object
                    date = datetime.strptime(date_str, "%Y-%m-%d")
                    # Use the time from the original start date
                    date = date.replace(
                        hour=start_date.hour,
                        minute=start_date.minute,
                        second=start_date.second
                    )
                    dates.append(date)
                except ValueError:
                    continue

    elif recurrence_type == 'weekly':
        # Weekly recurrence
        if 'days' in recurrence_details and 'weeks' in recurrence_details:
            days = recurrence_details['days']  # List of day indices (0=Monday, 6=Sunday)
            weeks = int(recurrence_details['weeks'])  # Number of weeks to generate

            # Start from the day after the start date to avoid duplicating the first date
            current_date = start_date + relativedelta(days=1)

            # Generate dates for the specified number of weeks
            for _ in range(weeks * 7):  # Check each day in the range
                # Check if the current day of week is in the selected days
                if current_date.weekday() in days:
                    # Create a new date with the same time as the original
                    event_date = current_date.replace(
                        hour=start_date.hour,
                        minute=start_date.minute,
                        second=start_date.second
                    )
                    dates.append(event_date)

                # Move to the next day
                current_date += relativedelta(days=1)

    elif recurrence_type == 'monthly':
        # Monthly recurrence
        if 'months' in recurrence_details:
            months = int(recurrence_details['months'])  # Number of months to generate

            if recurrence_details.get('type') == 'day_of_month':
                # Same day each month (e.g., 15th of every month)
                day = start_date.day

                for i in range(1, months + 1):
                    try:
                        # Create date for the same day in future months
                        next_date = start_date + relativedelta(months=i)
                        # Adjust for months with fewer days
                        last_day = calendar.monthrange(next_date.year, next_date.month)[1]
                        if day > last_day:
                            next_date = next_date.replace(day=last_day)
                        else:
                            next_date = next_date.replace(day=day)

                        dates.append(next_date)
                    except ValueError:
                        continue

            elif recurrence_details.get('type') == 'day_of_week':
                # Same position weekday (e.g., 3rd Tuesday of every month)
                week_number = int(recurrence_details.get('week_number', 1))
                day_of_week = int(recurrence_details.get('day_of_week', 0))

                for i in range(1, months + 1):
                    try:
                        # Move to the next month
                        next_month = start_date + relativedelta(months=i)
                        # Reset to the first day of the month
                        first_day = next_month.replace(day=1)

                        # Find the first occurrence of the specified day of week
                        while first_day.weekday() != day_of_week:
                            first_day += relativedelta(days=1)

                        # Calculate the target date based on week number
                        if week_number <= 4:
                            # For 1st to 4th occurrence
                            target_date = first_day + relativedelta(days=(week_number - 1) * 7)
                        else:
                            # For last occurrence
                            # Find the last day of the month
                            last_day = next_month.replace(
                                day=calendar.monthrange(next_month.year, next_month.month)[1]
                            )
                            # Find the last occurrence of the day of week
                            while last_day.weekday() != day_of_week:
                                last_day -= relativedelta(days=1)
                            target_date = last_day

                        # Set the time from the original start date
                        target_date = target_date.replace(
                            hour=start_date.hour,
                            minute=start_date.minute,
                            second=start_date.second
                        )

                        dates.append(target_date)
                    except ValueError:
                        continue

    # Sort dates chronologically
    return sorted(dates)
