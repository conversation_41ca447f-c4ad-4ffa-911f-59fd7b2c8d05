{% extends "base.html" %}

{% block content %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/setup.css') }}">

<div class="container-fluid dashboard-container">
    {% if not current_user.subscription or current_user.subscription.name == 'Free' %}
    <div class="alert alert-primary mt-1 mb-2 py-2 d-flex justify-content-between align-items-center" role="alert" style="font-size: 0.9rem;">
        <div class="text-center w-100">
            <i class="fas fa-star me-1"></i>
            <strong>Upgrade your plan to unlock all features!</strong> Get access to advanced analytics, premium tools, and more.
        </div>
        <a href="{{ url_for('activation.activating') }}" class="btn btn-primary btn-sm py-1 px-2">
            Upgrade Now
        </a>
    </div>
    {% endif %}

    <!-- Setup Dashboard Header -->
    <div class="setup-header">
        <h1><i class="fas fa-cog me-2"></i>Setup Dashboard</h1>
        <p class="text-muted">Configure your account settings and integrations</p>
    </div>

    <!-- Setup Options Grid -->
    <div class="setup-grid">
        <!-- Profile Setup Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-user-circle"></i>
                <h2>Profile</h2>
            </div>
            <div class="setup-card-body">
                <p>Manage your personal information, account settings, and preferences.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Update personal details</li>
                    <li><i class="fas fa-check-circle"></i> Change password</li>
                    <li><i class="fas fa-check-circle"></i> Set currency preferences</li>
                    <li><i class="fas fa-check-circle"></i> Manage notification settings</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('profile.profile') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>Manage Profile
                </a>
            </div>
        </div>

        <!-- Integrations Setup Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-plug"></i>
                <h2>Integrations</h2>
            </div>
            <div class="setup-card-body">
                <p>Connect and manage third-party platforms and services.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Shopify integration</li>
                    <li><i class="fas fa-check-circle"></i> TCGPlayer connection</li>
                    <li><i class="fas fa-check-circle"></i> eBay marketplace</li>
                    <li><i class="fas fa-check-circle"></i> API access management</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('integration.integrations') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>Manage Integrations
                </a>
            </div>
        </div>

        <!-- Pricing Setup Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-tags"></i>
                <h2>Pricing</h2>
            </div>
            <div class="setup-card-body">
                <p>Configure your pricing strategies and automation rules.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Price calculation rules</li>
                    <li><i class="fas fa-check-circle"></i> Automatic pricing</li>
                    <li><i class="fas fa-check-circle"></i> Bulk price adjustments</li>
                    <li><i class="fas fa-check-circle"></i> Price history tracking</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('reprice_logs.reprice_now') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>Manage Pricing
                </a>
            </div>
        </div>

        <!-- Buylist Setup Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-shopping-basket"></i>
                <h2>Buylist</h2>
            </div>
            <div class="setup-card-body">
                <p>Configure your buylist settings and purchase rules.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Buylist pricing rules</li>
                    <li><i class="fas fa-check-circle"></i> Purchase conditions</li>
                    <li><i class="fas fa-check-circle"></i> Automatic buylist updates</li>
                    <li><i class="fas fa-check-circle"></i> Buylist export options</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('buylist.buylist_dashboard') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>Manage Buylist
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    /* Setup Dashboard Specific Styles */
    .setup-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .setup-header h1 {
        font-size: 2rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
    }
    
    .setup-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }
    
    .setup-card {
        background: linear-gradient(135deg, rgba(26, 32, 44, 0.8), rgba(45, 55, 72, 0.8));
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.05);
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    
    .setup-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .setup-card-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .setup-card-header i {
        font-size: 2rem;
        color: var(--primary-color);
    }
    
    .setup-card-header h2 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #ffffff;
        margin: 0;
    }
    
    .setup-card-body {
        padding: 1.5rem;
        flex-grow: 1;
    }
    
    .setup-card-body p {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 1.5rem;
    }
    
    .setup-features-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .setup-features-list li {
        margin-bottom: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .setup-features-list li i {
        color: var(--success-color);
        font-size: 0.9rem;
    }
    
    .setup-card-footer {
        padding: 1.5rem;
        border-top: 1px solid rgba(255, 255, 255, 0.05);
        text-align: center;
    }
    
    .setup-card-footer .btn {
        width: 100%;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .setup-card-footer .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .setup-grid {
            grid-template-columns: 1fr;
        }
        
        .setup-header h1 {
            font-size: 1.75rem;
        }
    }
</style>
{% endblock %}
