import asyncio
import aiohttp
from motor.motor_asyncio import AsyncIOMotorClient
import logging
import time

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('delete_shuffled_items.log')
    ]
)

# MongoDB setup
client = AsyncIOMotorClient(
    '*******************************************************************',
    maxPoolSize=100
)
db = client.test
products_collection = db.shProducts
user_collection = db.user

# Constants
SHOPIFY_API_VERSION = "2023-04"
RATE_LIMIT_DELAY = 0.5  # 0.5 seconds = 2 requests per second

async def get_shuffled_user_credentials():
    """Get ShuffledLGS user credentials from database"""
    try:
        user = await user_collection.find_one({"username": "<PERSON>ffledLGS"})
        if not user:
            logging.error("ShuffledLGS user not found in database")
            return None, []
        
        shopify_store_name = user.get("shopifyStoreName")
        
        # Collect all available access tokens
        access_tokens = []
        for i in range(11):  # shopifyAccessToken through shopifyAccessToken10
            if i == 0:
                token_key = "shopifyAccessToken"
            else:
                token_key = f"shopifyAccessToken{i}"
            
            token = user.get(token_key)
            if token and token.strip():  # Only add non-empty tokens
                access_tokens.append(token)
        
        if not shopify_store_name or not access_tokens:
            logging.error("ShuffledLGS user missing Shopify credentials")
            return None, []
        
        logging.info(f"Found ShuffledLGS credentials - Store: {shopify_store_name}, Tokens: {len(access_tokens)}")
        return shopify_store_name, access_tokens
    
    except Exception as e:
        logging.error(f"Error getting user credentials: {e}")
        return None, []

async def get_tcg_items_count():
    """Get count of TCG items for ShuffledLGS"""
    try:
        count = await products_collection.count_documents({
            "username": "ShuffledLGS",
            "tcgItem": True
        })
        return count
    except Exception as e:
        logging.error(f"Error counting TCG items: {e}")
        return 0

async def get_tcg_items():
    """Get all TCG items for ShuffledLGS"""
    try:
        cursor = products_collection.find({
            "username": "ShuffledLGS",
            "tcgItem": True
        })
        items = await cursor.to_list(length=None)
        return items
    except Exception as e:
        logging.error(f"Error getting TCG items: {e}")
        return []

async def delete_shopify_product(session, shop_domain, access_token, product_id):
    """Delete a product from Shopify"""
    headers = {
        'X-Shopify-Access-Token': access_token,
        'Content-Type': 'application/json',
    }
    
    delete_url = f"https://{shop_domain}.myshopify.com/admin/api/{SHOPIFY_API_VERSION}/products/{product_id}.json"
    
    try:
        async with session.delete(delete_url, headers=headers) as response:
            if response.status == 200:
                logging.info(f"Successfully deleted product {product_id} from Shopify")
                return True
            elif response.status == 404:
                logging.warning(f"Product {product_id} not found in Shopify (already deleted?)")
                return True  # Consider this a success since the product is gone
            else:
                response_text = await response.text()
                logging.error(f"Failed to delete product {product_id} from Shopify. Status: {response.status}, Response: {response_text}")
                return False
    except Exception as e:
        logging.error(f"Error deleting product {product_id} from Shopify: {e}")
        return False

async def delete_from_database(product_id):
    """Delete a product from the database"""
    try:
        result = await products_collection.delete_one({
            "username": "ShuffledLGS",
            "id": product_id
        })
        if result.deleted_count > 0:
            logging.info(f"Successfully deleted product {product_id} from database")
            return True
        else:
            logging.warning(f"Product {product_id} not found in database")
            return False
    except Exception as e:
        logging.error(f"Error deleting product {product_id} from database: {e}")
        return False

async def delete_items_with_token(session, shop_domain, access_token, items, token_index):
    """Delete items using a specific access token"""
    headers = {
        'X-Shopify-Access-Token': access_token,
        'Content-Type': 'application/json',
    }
    
    success_count = 0
    failed_count = 0
    
    logging.info(f"Token {token_index + 1} starting deletion of {len(items)} items")
    
    for i, item in enumerate(items):
        product_id = item.get('id')
        if not product_id:
            logging.warning(f"Token {token_index + 1}: Item has no product ID, skipping")
            failed_count += 1
            continue
        
        delete_url = f"https://{shop_domain}.myshopify.com/admin/api/{SHOPIFY_API_VERSION}/products/{product_id}.json"
        
        try:
            # Delete from Shopify
            async with session.delete(delete_url, headers=headers) as response:
                if response.status == 200:
                    # Delete from database if Shopify deletion was successful
                    db_deleted = await delete_from_database(product_id)
                    if db_deleted:
                        success_count += 1
                        logging.info(f"Token {token_index + 1}: Successfully deleted product {product_id}")
                    else:
                        failed_count += 1
                        logging.error(f"Token {token_index + 1}: Failed to delete product {product_id} from database")
                elif response.status == 404:
                    # Product already deleted, still delete from database
                    db_deleted = await delete_from_database(product_id)
                    if db_deleted:
                        success_count += 1
                        logging.warning(f"Token {token_index + 1}: Product {product_id} not found in Shopify (already deleted?), removed from database")
                    else:
                        failed_count += 1
                        logging.warning(f"Token {token_index + 1}: Product {product_id} not found in Shopify or database")
                else:
                    failed_count += 1
                    response_text = await response.text()
                    logging.error(f"Token {token_index + 1}: Failed to delete product {product_id}. Status: {response.status}, Response: {response_text}")
            
            # Rate limiting: 2 requests per second per token
            await asyncio.sleep(RATE_LIMIT_DELAY)
            
        except Exception as e:
            failed_count += 1
            logging.error(f"Token {token_index + 1}: Error deleting product {product_id}: {e}")
        
        # Log progress every 100 deletions
        if (i + 1) % 100 == 0:
            logging.info(f"Token {token_index + 1}: Processed {i + 1}/{len(items)} items")
    
    logging.info(f"Token {token_index + 1} completed: {success_count} successful, {failed_count} failed")
    return success_count, failed_count

async def delete_items_parallel(session, shop_domain, access_tokens, items):
    """Delete items using multiple access tokens in parallel"""
    if not access_tokens:
        logging.error("No access tokens available")
        return 0, 0
    
    # Split items among available tokens
    num_tokens = len(access_tokens)
    chunk_size = len(items) // num_tokens
    
    tasks = []
    for i, token in enumerate(access_tokens):
        start_idx = i * chunk_size
        if i == num_tokens - 1:  # Last token gets remaining items
            end_idx = len(items)
        else:
            end_idx = start_idx + chunk_size
        
        token_items = items[start_idx:end_idx]
        if token_items:  # Only create task if there are items to delete
            task = delete_items_with_token(session, shop_domain, token, token_items, i)
            tasks.append(task)
    
    logging.info(f"Starting parallel deletion with {len(tasks)} tokens")
    
    # Run all deletion tasks in parallel
    results = await asyncio.gather(*tasks)
    
    # Sum up results
    total_success = sum(success for success, failed in results)
    total_failed = sum(failed for success, failed in results)
    
    return total_success, total_failed

async def delete_tcg_items():
    """Main function to delete TCG items"""
    # Get user credentials
    shop_domain, access_tokens = await get_shuffled_user_credentials()
    if not shop_domain or not access_tokens:
        logging.error("Cannot proceed without valid Shopify credentials")
        return
    
    # Get count of items to delete
    item_count = await get_tcg_items_count()
    if item_count == 0:
        logging.info("No TCG items found for ShuffledLGS")
        return
    
    print(f"\nFound {item_count} TCG items for ShuffledLGS to delete.")
    print(f"This will delete products from Shopify store: {shop_domain}.myshopify.com")
    print("WARNING: This action cannot be undone!")
    print(f"This will use {len(access_tokens)} access tokens in parallel (2 requests per second per token).")
    print(f"Estimated time: ~{(item_count / len(access_tokens) / 2) / 60:.1f} minutes")
    
    # Ask for confirmation
    confirmation = input(f"\nAre you sure you want to delete all {item_count} TCG items? Type 'DELETE' to confirm: ")
    if confirmation != "DELETE":
        print("Deletion cancelled.")
        return
    
    # Get all items to delete
    items = await get_tcg_items()
    if not items:
        logging.error("Failed to retrieve items for deletion")
        return
    
    logging.info(f"Starting parallel deletion of {len(items)} TCG items using {len(access_tokens)} tokens")
    
    async with aiohttp.ClientSession() as session:
        # Delete items using multiple tokens in parallel
        success_count, failed_count = await delete_items_parallel(session, shop_domain, access_tokens, items)
        
        logging.info(f"Parallel deletion completed. Success: {success_count}, Failed: {failed_count}")
        print(f"\nParallel deletion completed:")
        print(f"Successfully deleted: {success_count} items")
        print(f"Failed to delete: {failed_count} items")
        print(f"Check delete_shuffled_items.log for detailed logs")

if __name__ == "__main__":
    asyncio.run(delete_tcg_items())
