{% extends "base.html" %}
{% block title %}Edit Event{% endblock %}
{% block content %}
<style>
    /* Set the background color to match dashboard */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
    }

    .main-content {
        background-color: #6b21a8;
    }
    
    /* Form styling */
    .form-card {
        border-radius: 12px; 
        background-color: rgba(25, 25, 39, 0.8); 
        border: 1px solid rgba(255, 255, 255, 0.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .form-section {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding-bottom: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .form-section:last-child {
        border-bottom: none;
    }
    
    .form-control, .form-select {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 8px;
    }
    
    .form-control:focus, .form-select:focus {
        background-color: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
        box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
    }
    
    /* Dark mode dropdown styling */
    .form-select option {
        background-color: #1e293b;
        color: white;
    }
    
    /* For Firefox */
    .form-select:focus {
        color: white;
        background-color: rgba(255, 255, 255, 0.15);
    }
    
    /* For Chrome/Safari/Edge */
    @media screen and (-webkit-min-device-pixel-ratio:0) {
        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
        }
    }
    
    /* Global styles for select dropdowns */
    select option,
    .dropdown-menu,
    .dropdown-item {
        background-color: #1e293b !important;
        color: white !important;
    }
    
    /* For webkit browsers */
    select::-webkit-scrollbar {
        width: 8px;
    }
    
    select::-webkit-scrollbar-track {
        background: #16213e;
    }
    
    select::-webkit-scrollbar-thumb {
        background-color: #6b21a8;
        border-radius: 20px;
    }
    
    /* Section styling with appropriate colors */
    .section-basic { border-left: 4px solid #2ecc71; }
    .section-datetime { border-left: 4px solid #3498db; }
    .section-capacity { border-left: 4px solid #e74c3c; }
    .section-details { border-left: 4px solid #9b59b6; }
    
    /* Form check styling */
    .form-check-input {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
    }
    
    .form-check-input:checked {
        background-color: #2ecc71;
        border-color: #2ecc71;
    }
    
    /* Alert styling */
    .alert-info {
        background-color: rgba(52, 152, 219, 0.1);
        border-color: rgba(52, 152, 219, 0.2);
        color: rgba(255, 255, 255, 0.9);
    }
    
    .alert-warning {
        background-color: rgba(241, 196, 15, 0.1);
        border-color: rgba(241, 196, 15, 0.2);
        color: rgba(255, 255, 255, 0.9);
    }
</style>

<div class="container mt-5">
    <div class="card form-card">
        <div class="card-header d-flex align-items-center" style="background-color: rgba(52, 152, 219, 0.2); border-bottom: 1px solid rgba(255, 255, 255, 0.05);">
            <div class="icon-wrapper me-3" style="width: 40px; height: 40px; border-radius: 10px; background-color: rgba(52, 152, 219, 0.3); display: flex; align-items: center; justify-content: center;">
                <i class="fas fa-edit" style="color: #3498db; font-size: 20px;"></i>
            </div>
            <h3 class="text-white mb-0">Edit Event</h3>
        </div>
        <div class="card-body text-white">
            {% if event.is_recurring and not event.is_recurrence_parent %}
                <div class="alert alert-warning mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span>This is part of a recurring event series. Changes made here will only affect this specific instance.</span>
                </div>
            {% endif %}
            
            <form id="editEventForm" method="post">
                <!-- Basic Info Section -->
                <div class="form-section section-basic p-3">
                    <h4 class="mb-3">Basic Information</h4>
                    
                    <div class="mb-3">
                        <label for="eventTitle" class="form-label">Event Title*</label>
                        <input type="text" class="form-control" id="eventTitle" name="title" value="{{ event.title }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="eventDescription" class="form-label">Event Description*</label>
                        <textarea class="form-control" id="eventDescription" name="description" rows="4" required>{{ event.description }}</textarea>
                    </div>

                    <!-- Event Image Section -->
                    {% if event.event_image_url %}
                        <div class="mb-3">
                            <label class="form-label">Current Image:</label>
                            <div class="position-relative d-inline-block">
                                <img src="{{ event.event_image_url }}"
                                     alt="{{ event.title }} Image"
                                     class="img-fluid rounded"
                                     style="max-height: 200px; max-width: 100%;">
                                <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1" id="removeCurrentImage" style="border-radius: 50%; width: 30px; height: 30px; padding: 0;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    {% endif %}

                    <div class="mb-3">
                        <label for="eventImage" class="form-label">{% if event.event_image_url %}Replace Image{% else %}Event Banner/Image{% endif %}</label>
                        <input type="file" class="form-control" id="eventImage" accept="image/*">
                        <small class="form-text text-white-50">Upload a banner or image for your event. Supported formats: PNG, JPG, JPEG, GIF, WEBP</small>
                    </div>

                    <!-- Image preview -->
                    <div id="imagePreview" style="display: none;" class="mb-3">
                        <label class="form-label">Preview:</label>
                        <div class="position-relative d-inline-block">
                            <img id="previewImg" src="" alt="Event Image Preview" class="img-fluid rounded" style="max-height: 200px; max-width: 100%;">
                            <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1" id="removeImage" style="border-radius: 50%; width: 30px; height: 30px; padding: 0;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="mt-2">
                            <small class="text-white-50" id="imageInfo"></small>
                        </div>
                    </div>

                    <!-- Hidden fields to store image data -->
                    <input type="hidden" id="eventImageUrl" name="event_image_url" value="{{ event.event_image_url or '' }}">
                    <input type="hidden" id="eventImageFilename" name="event_image_filename" value="{{ event.event_image_filename or '' }}">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="gameSelection" class="form-label">Game</label>
                            <select class="form-select" id="gameSelection" name="game">
                                <option value="">-- Select Game --</option>
                                {% for game in games %}
                                <option value="{{ game }}" {% if event.game == game %}selected{% endif %}>{{ game }}</option>
                                {% endfor %}
                                <option value="custom" {% if event.custom_game %}selected{% endif %}>Other (Custom Event)</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3" id="customGameContainer" style="display: {% if event.custom_game %}block{% else %}none{% endif %};">
                            <label for="customGame" class="form-label">Custom Game Name</label>
                            <input type="text" class="form-control" id="customGame" name="custom_game_name" value="{{ event.custom_game_name }}">
                        </div>
                    </div>
                </div>
                
                <!-- Date & Time Section -->
                <div class="form-section section-datetime p-3">
                    <h4 class="mb-3">Date & Time</h4>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="eventDate" class="form-label">Event Date*</label>
                            <input type="date" class="form-control" id="eventDate" name="event_date" value="{{ event.start_datetime.strftime('%Y-%m-%d') }}" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="eventTime" class="form-label">Start Time*</label>
                            <input type="time" class="form-control" id="eventTime" name="event_time" value="{{ event.start_datetime.strftime('%H:%M') }}" required>
                        </div>
                    </div>
                    
                    {% if event.is_recurring and event.is_recurrence_parent %}
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <span>This is a recurring event. Editing the recurrence pattern is not supported. If you need to change the recurrence pattern, please delete this event and create a new one.</span>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Capacity & Tickets Section -->
                <div class="form-section section-capacity p-3">
                    <h4 class="mb-3">Capacity & Pricing</h4>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="maxAttendees" class="form-label">Maximum Attendees*</label>
                            <input type="number" class="form-control" id="maxAttendees" name="max_attendees" min="1" value="{{ event.max_attendees }}" required>
                            <small class="form-text text-white-50">This will determine the number of tickets available for purchase.</small>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="ticketPrice" class="form-label">Ticket Price (£)*</label>
                            <input type="number" class="form-control" id="ticketPrice" name="ticket_price" min="0" step="0.01" value="{{ event.ticket_price }}" required>
                            <small class="form-text text-white-50">Amount charged to customers</small>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="costPerAttendee" class="form-label">Cost Per Attendee (£)</label>
                            <input type="number" class="form-control" id="costPerAttendee" name="cost_per_attendee" min="0" step="0.01" value="{{ event.cost_per_attendee }}">
                            <small class="form-text text-white-50">Your cost for running this event</small>
                        </div>
                    </div>
                    
                    <div class="mb-3 p-3 rounded" style="background-color: rgba(46, 204, 113, 0.1);">
                        <h5>Profit Calculation</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <p class="mb-1">Profit per ticket:</p>
                                <h4 id="profitPerTicket">£{{ (event.ticket_price - event.cost_per_attendee)|round(2) }}</h4>
                            </div>
                            <div class="col-md-4">
                                <p class="mb-1">Total potential revenue:</p>
                                <h4 id="totalRevenue">£{{ (event.ticket_price * event.max_attendees)|round(2) }}</h4>
                            </div>
                            <div class="col-md-4">
                                <p class="mb-1">Total potential profit:</p>
                                <h4 id="totalProfit">£{{ ((event.ticket_price - event.cost_per_attendee) * event.max_attendees)|round(2) }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Event Details Section -->
                <div class="form-section section-details p-3">
                    <h4 class="mb-3">Event Details</h4>
                    
                    <div class="mb-3">
                        <label for="eventType" class="form-label">Event Type*</label>
                        <select class="form-select" id="eventType" name="event_type" required>
                            <option value="" disabled>-- Select Event Type --</option>
                            <option value="tournament" {% if event.event_type == 'tournament' %}selected{% endif %}>Tournament</option>
                            <option value="pre-release" {% if event.event_type == 'pre-release' %}selected{% endif %}>Pre-Release</option>
                            <option value="draft" {% if event.event_type == 'draft' %}selected{% endif %}>Draft</option>
                            <option value="sealed" {% if event.event_type == 'sealed' %}selected{% endif %}>Sealed</option>
                            <option value="casual" {% if event.event_type == 'casual' %}selected{% endif %}>Casual Play</option>
                            <option value="championship" {% if event.event_type == 'championship' %}selected{% endif %}>Championship</option>
                            <option value="other" {% if event.event_type == 'other' %}selected{% endif %}>Other</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="prizeDetails" class="form-label">Prize Details</label>
                        <textarea class="form-control" id="prizeDetails" name="prize_details" rows="3" placeholder="Describe prizes, structure, qualification criteria, etc.">{{ event.prize_details }}</textarea>
                    </div>
                </div>



                <div class="text-end mt-4">
                    <a href="{{ url_for('events.view_events') }}" class="btn btn-outline-light me-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <!-- Toasts will be added here dynamically -->
</div>

<script>
    // Toast notification function
    function showToast(message, type = 'info') {
        const toastContainer = document.querySelector('.toast-container');
        
        // Create toast element
        const toastEl = document.createElement('div');
        toastEl.className = `toast align-items-center text-white border-0`;
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');
        
        // Set background color based on type
        let bgColor, icon;
        switch(type) {
            case 'success':
                bgColor = '#2ecc71';
                icon = 'fa-check-circle';
                break;
            case 'error':
                bgColor = '#e74c3c';
                icon = 'fa-exclamation-circle';
                break;
            case 'warning':
                bgColor = '#f1c40f';
                icon = 'fa-exclamation-triangle';
                break;
            default: // info
                bgColor = '#3498db';
                icon = 'fa-info-circle';
        }
        
        toastEl.style.backgroundColor = bgColor;
        
        // Create toast content
        toastEl.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas ${icon} me-2"></i> ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        // Add toast to container
        toastContainer.appendChild(toastEl);
        
        // Initialize and show toast
        const toast = new bootstrap.Toast(toastEl, {
            animation: true,
            autohide: true,
            delay: 5000
        });
        toast.show();
        
        // Remove toast after it's hidden
        toastEl.addEventListener('hidden.bs.toast', function() {
            toastEl.remove();
        });
    }
    document.addEventListener('DOMContentLoaded', function() {
        // Image upload functionality
        const eventImageInput = document.getElementById('eventImage');
        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        const imageInfo = document.getElementById('imageInfo');
        const removeImageBtn = document.getElementById('removeImage');
        const removeCurrentImageBtn = document.getElementById('removeCurrentImage');
        const eventImageUrl = document.getElementById('eventImageUrl');
        const eventImageFilename = document.getElementById('eventImageFilename');

        if (eventImageInput) {
            eventImageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (!file) return;

                // Validate file type
                const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    showToast('Invalid file type. Please select a PNG, JPG, JPEG, GIF, or WEBP image.', 'error');
                    eventImageInput.value = '';
                    return;
                }

                // Validate file size (max 5MB)
                const maxSize = 5 * 1024 * 1024; // 5MB
                if (file.size > maxSize) {
                    showToast('File size too large. Please select an image smaller than 5MB.', 'error');
                    eventImageInput.value = '';
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    imageInfo.textContent = `${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
                    imagePreview.style.display = 'block';
                };
                reader.readAsDataURL(file);

                // Upload the image
                uploadEventImage(file);
            });
        }

        if (removeImageBtn) {
            removeImageBtn.addEventListener('click', function() {
                // Clear the new image
                eventImageInput.value = '';
                imagePreview.style.display = 'none';
                previewImg.src = '';
                eventImageUrl.value = '';
                eventImageFilename.value = '';
                imageInfo.textContent = '';
            });
        }

        if (removeCurrentImageBtn) {
            removeCurrentImageBtn.addEventListener('click', function() {
                // Clear the current image
                eventImageUrl.value = '';
                eventImageFilename.value = '';
                removeCurrentImageBtn.parentElement.style.display = 'none';
                showToast('Current image will be removed when you save the event.', 'info');
            });
        }

        function uploadEventImage(file) {
            const formData = new FormData();
            formData.append('image', file);

            // Show upload progress
            showToast('Uploading image...', 'info');

            fetch('{{ url_for("events.upload_event_image") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Store the image URL and filename for form submission
                    eventImageUrl.value = data.image_url;
                    eventImageFilename.value = data.filename;
                    showToast('Image uploaded successfully!', 'success');
                } else {
                    showToast('Error uploading image: ' + data.message, 'error');
                    // Clear the preview on error
                    if (removeImageBtn) removeImageBtn.click();
                }
            })
            .catch(error => {
                console.error('Error uploading image:', error);
                showToast('Error uploading image. Please try again.', 'error');
                // Clear the preview on error
                if (removeImageBtn) removeImageBtn.click();
            });
        }

        // Game selection handling
        const gameSelection = document.getElementById('gameSelection');
        const customGameContainer = document.getElementById('customGameContainer');
        
        gameSelection.addEventListener('change', function() {
            if (this.value === 'custom') {
                customGameContainer.style.display = 'block';
                document.getElementById('customGame').setAttribute('required', 'required');
            } else {
                customGameContainer.style.display = 'none';
                document.getElementById('customGame').removeAttribute('required');
            }
        });
        
        // Profit calculation
        const ticketPrice = document.getElementById('ticketPrice');
        const costPerAttendee = document.getElementById('costPerAttendee');
        const maxAttendees = document.getElementById('maxAttendees');
        const profitPerTicket = document.getElementById('profitPerTicket');
        const totalRevenue = document.getElementById('totalRevenue');
        const totalProfit = document.getElementById('totalProfit');
        
        function updateProfitCalculation() {
            const price = parseFloat(ticketPrice.value) || 0;
            const cost = parseFloat(costPerAttendee.value) || 0;
            const attendees = parseInt(maxAttendees.value) || 0;
            
            const profit = price - cost;
            const revenue = price * attendees;
            const totalProfitValue = profit * attendees;
            
            profitPerTicket.textContent = `£${profit.toFixed(2)}`;
            totalRevenue.textContent = `£${revenue.toFixed(2)}`;
            totalProfit.textContent = `£${totalProfitValue.toFixed(2)}`;
            
            // Change color based on profit
            if (profit > 0) {
                profitPerTicket.style.color = '#2ecc71';
                totalProfit.style.color = '#2ecc71';
            } else if (profit < 0) {
                profitPerTicket.style.color = '#e74c3c';
                totalProfit.style.color = '#e74c3c';
            } else {
                profitPerTicket.style.color = '#ffffff';
                totalProfit.style.color = '#ffffff';
            }
        }
        
        ticketPrice.addEventListener('input', updateProfitCalculation);
        costPerAttendee.addEventListener('input', updateProfitCalculation);
        maxAttendees.addEventListener('input', updateProfitCalculation);
        
        // Form submission
        const form = document.getElementById('editEventForm');
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validate form
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }
            
            // Submit form via AJAX
            const formData = new FormData(form);
            
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Saving...';
            
            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showToast(data.message, 'success');
                    // Redirect to events list after a short delay
                    setTimeout(() => {
                        window.location.href = '{{ url_for("events.view_events") }}';
                    }, 1000);
                } else {
                    // Show error message
                    showToast('Error: ' + data.message, 'error');
                    // Reset button
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalBtnText;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('An error occurred while updating the event. Please try again.', 'error');
                // Reset button
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;
            });
        });
        
        // Initialize profit calculation
        updateProfitCalculation();
    });
</script>
{% endblock %}
