from flask import Blueprint, render_template, request, jsonify, send_file
from flask_login import login_required, current_user
from middlewares.auth_middleware import subscription_required
from pymongo import MongoClient, ASCENDING
from datetime import datetime, timedelta, timezone
from cache_config import cache
import time
from collections import defaultdict, Counter
import csv
import io
import logging
import re
import os
import openai
import json
import traceback
from bson import json_util, ObjectId
import traceback
import ast
import requests
from utils.currency_converter import convert_currency, get_currency_symbol
from .comics_routes import comics_scanning_bp
from .cardmarket_routes import cardmarket_bp
from .eu_purchasing_routes import eu_purchasing_bp  # Updated import to match blueprint name
from .shopify_export_routes import shopify_export_bp
from routes.admin_routes import ensure_user_subscription
from routes.ebay_auth_routes import refresh_ebay_token_if_needed
from routes.ticket_routes import count_open_tickets
from functools import wraps, lru_cache
from models.user_model import User, Subscription
from mongoengine.errors import DoesNotExist

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API Keys (Replace these with secure environment variables in production)
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY', '********************************************************')

# Initialize OpenAI API key
openai.api_key = OPENAI_API_KEY

# MongoDB Configuration
mongo_uri = 'mongodb://admin:Reggie2805!@*************:27017/?authSource=admin'
mongo_dbname = 'test'
mongo_client = MongoClient(mongo_uri)
db = mongo_client[mongo_dbname]
shOrders_collection = db['shOrders']
catalog_collection = db['catalog']
tickets_collection = db['tickets']
shProducts_collection = db['shProducts']
prices_collection = db['prices']
saleshistories_collection = db['saleshistories']
wallet_collection = db['wallet']

updates_collection = db['updates']
staged_inventory_collection = db['staged_inventory']

dashboard_bp = Blueprint('dashboard', __name__)
dashboard_bp.register_blueprint(comics_scanning_bp)
dashboard_bp.register_blueprint(cardmarket_bp, url_prefix='/cardmarket')
dashboard_bp.register_blueprint(eu_purchasing_bp, url_prefix='/eu-purchasing')
dashboard_bp.register_blueprint(shopify_export_bp, url_prefix='/shopify-export')

def check_subscription(f):
    """Decorator to ensure user has a valid subscription"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if current_user.is_authenticated:
            try:
                # Get user object from database to ensure we have latest data
                user_obj = User.objects(username=current_user.username).first()
                if not user_obj:
                    logger.error(f"User not found in check_subscription: {current_user.username}")
                    return render_template('error.html', error_message="User not found")

                # Get subscription name before ensuring subscription
                old_subscription = user_obj.get_subscription_name()

                # Ensure subscription is valid
                user_obj = ensure_user_subscription(user_obj)

                # Get new subscription name after ensuring subscription
                new_subscription = user_obj.get_subscription_name()

                # Log if subscription changed
                if old_subscription != new_subscription:
                    logger.info(f"Subscription changed for user {user_obj.username}: {old_subscription} -> {new_subscription}")

                # Verify subscription reference is valid
                if user_obj.subscription:
                    try:
                        subscription = Subscription.objects.get(id=user_obj.subscription.id)
                        if subscription.name != user_obj.subscription.name:
                            logger.warning(f"Subscription name mismatch for user {user_obj.username}: {user_obj.subscription.name} != {subscription.name}")
                            # If current subscription is Lifetime but reference is wrong type, create new Lifetime
                            if user_obj.subscription.name == 'Lifetime':
                                try:
                                    new_subscription = Subscription(name='Lifetime').save()
                                    user_obj.subscription = new_subscription
                                    user_obj.free_subscription = None
                                    user_obj.save()
                                    logger.info(f"Created new Lifetime subscription for user {user_obj.username} due to type mismatch")
                                except Exception as e:
                                    logger.error(f"Error creating new Lifetime subscription: {str(e)}")
                                    logger.error(traceback.format_exc())
                                    user_obj.subscription = None
                                    user_obj.free_subscription = {'start_date': datetime.utcnow()}
                                    user_obj.save()
                                    logger.info(f"Fallback to free subscription after Lifetime creation error")
                            else:
                                # For non-Lifetime subscriptions, use the correct subscription from database
                                user_obj.subscription = subscription
                                # Clear any lingering free subscription
                                if user_obj.free_subscription:
                                    user_obj.free_subscription = None
                                    logger.info(f"Cleared lingering free subscription for paid user {user_obj.username}")
                                user_obj.save()
                            logger.info(f"Fixed subscription name mismatch for user {user_obj.username}")
                    except DoesNotExist:
                        logger.error(f"Invalid subscription reference for user {user_obj.username}")
                        # If subscription was supposed to be Lifetime, create new one
                        if user_obj.subscription.name == 'Lifetime':
                            try:
                                subscription = Subscription(name='Lifetime').save()
                                user_obj.subscription = subscription
                                user_obj.free_subscription = None
                                user_obj.save()
                                logger.info(f"Recreated Lifetime subscription for user {user_obj.username}")
                            except Exception as e:
                                logger.error(f"Error recreating Lifetime subscription: {str(e)}")
                                logger.error(traceback.format_exc())
                                user_obj.subscription = None
                                user_obj.free_subscription = {'start_date': datetime.utcnow()}
                                user_obj.save()
                                logger.info(f"Fallback to free subscription after Lifetime recreation error")
                        else:
                            # For non-Lifetime subscriptions with invalid reference, fallback to free
                            user_obj.subscription = None
                            user_obj.free_subscription = {'start_date': datetime.utcnow()}
                            user_obj.save()
                            logger.info(f"Set up free subscription for user {user_obj.username} after invalid reference")
                        logger.info(f"Fixed invalid subscription reference for user {user_obj.username}")
                    except Exception as e:
                        logger.error(f"Unexpected error verifying subscription: {str(e)}")
                        logger.error(traceback.format_exc())
                        # Ensure user has at least a free subscription
                        user_obj.subscription = None
                        user_obj.free_subscription = {'start_date': datetime.utcnow()}
                        user_obj.save()
                        logger.info(f"Fallback to free subscription after unexpected error")
                elif user_obj.free_subscription and not isinstance(user_obj.free_subscription, dict):
                    # Fix invalid free_subscription format
                    logger.warning(f"Invalid free_subscription format for user {user_obj.username}")
                    user_obj.free_subscription = {'start_date': datetime.utcnow()}
                    user_obj.save()
                    logger.info(f"Fixed invalid free_subscription format for user {user_obj.username}")

                # Update current_user with latest subscription data
                current_user.subscription = user_obj.subscription
                current_user.free_subscription = user_obj.free_subscription

                # Log subscription status
                logger.info(f"Subscription check passed for user {user_obj.username}: {new_subscription}")

                # Save any changes back to the database
                try:
                    user_obj.save()
                except Exception as save_error:
                    logger.error(f"Error saving user subscription changes: {str(save_error)}")
                    logger.error(traceback.format_exc())
                    # Continue with request even if save fails

            except Exception as e:
                logger.error(f"Error in check_subscription for user {current_user.username}: {str(e)}")
                logger.error(traceback.format_exc())
                # Continue with request but ensure user has at least a free subscription
                if not current_user.free_subscription:
                    current_user.subscription = None
                    current_user.free_subscription = {'start_date': datetime.utcnow()}
                    try:
                        # Try to save the free subscription
                        user_obj = User.objects(username=current_user.username).first()
                        if user_obj:
                            user_obj.subscription = None
                            user_obj.free_subscription = {'start_date': datetime.utcnow()}
                            user_obj.save()
                            logger.info(f"Created fallback free subscription for user {current_user.username}")
                    except Exception as save_error:
                        logger.error(f"Error saving fallback free subscription: {str(save_error)}")
                        logger.error(traceback.format_exc())
        return f(*args, **kwargs)
    return decorated_function

def check_ebay_token(f):
    """Decorator to ensure eBay token is refreshed"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if current_user.is_authenticated and hasattr(current_user, 'ebay_active') and current_user.ebay_active:
            try:
                refresh_ebay_token_if_needed()
            except Exception as e:
                logger.error(f"Error refreshing eBay token: {str(e)}")
                # Continue with request even if refresh fails
        return f(*args, **kwargs)
    return decorated_function

# Cache functions
@lru_cache(maxsize=128)
def get_cached_user_data(username, timestamp):
    # timestamp is used to invalidate cache every 5 minutes
    user = db['user'].find_one({"username": username})
    return {
        'shopify_connected': bool(user and user.get('shopifyAccessToken')),
        'auto_update_status': bool(db['autoUpdate'].find_one({"username": username})),
        'auto_pricing_status': bool(db['autopricerShopify'].find_one({"username": username}))
    }

@lru_cache(maxsize=128)
def get_cached_counts(username, timestamp):
    # timestamp is used to invalidate cache every minute
    staged_count = staged_inventory_collection.count_documents({"username": username})
    mobile_uploads_count = 0

    # Get mobile uploads count
    uploads_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'mobile_uploads', username)
    if os.path.exists(uploads_dir):
        mobile_uploads_count = len([f for f in os.listdir(uploads_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))])

    pending_buylist_orders = db['buylistOrders'].count_documents({
        "username": username,
        "$and": [
            {"orderStatus": {"$nin": ["Complete", "amendment", "accepted"]}},
            {"$or": [
                {"isComplete": False},
                {"isComplete": {"$exists": False}}
            ]}
        ]
    })









    return {
        'staged_count': staged_count,
        'pending_buylist_orders': pending_buylist_orders,
        'mobile_uploads_count': mobile_uploads_count
    }

@cache.memoize(timeout=21600)  # Cache for 6 hours (21600 seconds)
def get_top_sales_data(game_filter=None, username=None, force_currency_conversion=True):
    """
    Get top 10 sales from saleshistories collection based on last 24 hours volume
    Optionally filter by specific game
    Results are cached for 6 hours
    If username is provided, includes user's inventory and price data
    Prices are converted to the user's currency if username is provided
    
    Improved version with optimized MongoDB aggregation pipeline and data processing
    """
    logger.info(f"Fetching top TCGPlayer sales data for game: {game_filter or 'All Games'}, username: {username}")
    try:
        # Get user's currency preference upfront to avoid repeated database calls
        user_currency = 'USD'  # Default currency
        user_obj = None
        
        if username:
            user_obj = User.objects(username=username).only('currency').first()
            if user_obj and hasattr(user_obj, 'currency') and user_obj.currency:
                user_currency = user_obj.currency
                logger.info(f"Using currency {user_currency} for user {username}")
        
        # Base match condition
        match_condition = {
            "salesStatistics.last24Hours.total": {"$exists": True, "$gt": 0}
        }

        # Add game filter if specified
        if game_filter and game_filter.strip():
            match_condition["gameName"] = game_filter.strip()

        # Improved aggregation pipeline with more processing in MongoDB
        pipeline = [
            # Match records with sales data in last 24 hours
            {
                "$match": match_condition
            },
            # Add a field to store the most recent sales data per variant
            # Using a simpler approach to avoid issues with null/None in MongoDB Python driver
            {
                "$addFields": {
                    "processedSalesData": {
                        "$cond": {
                            "if": {"$isArray": "$salesData.data"},
                            "then": {
                                "$filter": {
                                    "input": "$salesData.data",
                                    "as": "sale",
                                    "cond": {"$gt": [{"$type": "$$sale"}, "missing"]}
                                }
                            },
                            "else": []
                        }
                    }
                }
            },
            # Process the sales data to extract relevant fields
            {
                "$addFields": {
                    "processedSalesData": {
                        "$map": {
                            "input": "$processedSalesData",
                            "as": "sale",
                            "in": {
                                "variant": {"$ifNull": ["$$sale.variant", "Standard"]},
                                "orderDate": "$$sale.orderDate",
                                "price": {"$ifNull": ["$$sale.purchasePrice", 0]},
                                "quantity": {"$ifNull": ["$$sale.quantity", 1]}
                            }
                        }
                    }
                }
            },
            # Project the fields we need including variant pricing data and sales data
            {
                "$project": {
                    "name": 1,
                    "expansionName": 1,
                    "gameName": 1,
                    "productId": 1,
                    "volume": "$salesStatistics.last24Hours.total",
                    "salesStatistics": 1,
                    "processedSalesData": 1,
                    "isSealed": 1,
                    "isSingle": 1
                }
            },
            # Sort by volume descending
            {
                "$sort": {"volume": -1}
            },
            # Limit to top 10
            {
                "$limit": 10
            }
        ]

        # Execute the aggregation pipeline with allowDiskUse for large datasets
        results = list(saleshistories_collection.aggregate(pipeline, allowDiskUse=True))
        
        # Batch fetch user's inventory and price data for all products at once if username is provided
        user_product_data = {}
        if username and results:
            product_ids = [item.get('productId') for item in results if item.get('productId')]
            if product_ids:
                user_product_data = batch_get_user_product_data(username, product_ids)
        
        # Process results to create separate rows for each variant with sales data analysis
        processed_results = []
        for rank, item in enumerate(results, 1):
            product_id = item.get('productId')
            
            # Get user's inventory and price data if username is provided
            user_inventory = 0
            user_price = None
            
            if username and product_id and product_id in user_product_data:
                user_inventory = user_product_data[product_id].get('inventory', 0)
                user_price = user_product_data[product_id].get('price')
            
            base_item = {
                'name': item.get('name'),
                'expansionName': item.get('expansionName'),
                'gameName': item.get('gameName'),
                'productId': item.get('productId'),
                'volume': item.get('volume'),
                'isSealed': item.get('isSealed', False),
                'isSingle': item.get('isSingle', False),
                'rank': rank,
                'userInventory': user_inventory,
                'userPrice': user_price
            }

            # Extract variant pricing data and sales data
            sales_stats = item.get('salesStatistics', {})
            by_variant = sales_stats.get('byVariant', {})
            processed_sales_data = item.get('processedSalesData', [])
            
            # Process sales data more efficiently
            variant_sales = {}
            last_sales = {}
            
            # Group sales by variant and find the most recent sale for each variant
            for sale in processed_sales_data:
                variant = sale.get('variant', 'Standard')
                quantity = sale.get('quantity', 1)
                
                # Count total sales per variant
                variant_sales[variant] = variant_sales.get(variant, 0) + quantity
                
                # Track the most recent sale for each variant
                sale_date = sale.get('orderDate')
                if sale_date and (variant not in last_sales or sale_date > last_sales[variant].get('orderDate', '')):
                    last_sales[variant] = {
                        'orderDate': sale_date,
                        'price': sale.get('price', 0),
                        'quantity': quantity
                    }
            
            # Currency conversion function with batch processing capability
            def convert_prices_batch(prices_dict, from_currency, to_currency):
                if from_currency == to_currency:
                    return prices_dict
                
                converted_prices = {}
                for key, value in prices_dict.items():
                    if value > 0:
                        converted_prices[key] = convert_currency(value, from_currency, to_currency)
                    else:
                        converted_prices[key] = 0
                return converted_prices

            variants_added = False
            for variant_name, variant_data in by_variant.items():
                if isinstance(variant_data, dict):
                    variant_item = base_item.copy()

                    # Get sales count for this variant
                    variant_volume = variant_sales.get(variant_name, 0)
                    last_sale = last_sales.get(variant_name)

                    # Convert prices from USD to user's currency if username is provided
                    average_price = variant_data.get('averagePrice', 0)
                    median_price = variant_data.get('medianPrice', 0)
                    lowest_price = variant_data.get('lowestPrice', 0)
                    highest_price = variant_data.get('highestPrice', 0)
                    last_sale_price = last_sale.get('price', 0) if last_sale else 0
                    
                    # Always convert prices if username is provided and force_currency_conversion is True
                    if username and (user_currency != 'USD' or force_currency_conversion):
                        logger.info(f"Converting prices from USD to {user_currency} for user {username}")
                        average_price = convert_currency(average_price, 'USD', user_currency) if average_price > 0 else 0
                        median_price = convert_currency(median_price, 'USD', user_currency) if median_price > 0 else 0
                        lowest_price = convert_currency(lowest_price, 'USD', user_currency) if lowest_price > 0 else 0
                        highest_price = convert_currency(highest_price, 'USD', user_currency) if highest_price > 0 else 0
                        last_sale_price = convert_currency(last_sale_price, 'USD', user_currency) if last_sale_price > 0 else 0
                    
                    variant_item.update({
                        'variantName': variant_name,
                        'variantVolume': variant_volume,
                        'variantCount': variant_data.get('count', 0),
                        'averagePrice': average_price,
                        'medianPrice': median_price,
                        'lowestPrice': lowest_price,
                        'highestPrice': highest_price,
                        'priceTrend': variant_data.get('priceTrend', 'stable'),
                        'percentChange': variant_data.get('percentChange', 0),
                        'isIncreasing': variant_data.get('isIncreasing', False),
                        'isDecreasing': variant_data.get('isDecreasing', False),
                        'lastSaleDate': last_sale.get('orderDate') if last_sale else None,
                        'lastSalePrice': last_sale_price,
                        'isFirstVariant': not variants_added,  # Mark first variant for styling
                        'currencySymbol': get_currency_symbol(user_currency) if username else '$'
                    })
                    processed_results.append(variant_item)
                    variants_added = True

            # If no variants found in byVariant but we have sales data, create variants from sales
            if not variants_added and variant_sales:
                for variant_name, variant_volume in variant_sales.items():
                    last_sale = last_sales.get(variant_name)
                    variant_item = base_item.copy()
                    # Convert last sale price if needed
                    last_sale_price = last_sale.get('price', 0) if last_sale else 0
                    if username and (user_currency != 'USD' or force_currency_conversion) and last_sale_price > 0:
                        last_sale_price = convert_currency(last_sale_price, 'USD', user_currency)
                    
                    variant_item.update({
                        'variantName': variant_name,
                        'variantVolume': variant_volume,
                        'variantCount': 0,
                        'averagePrice': 0,
                        'medianPrice': 0,
                        'lowestPrice': 0,
                        'highestPrice': 0,
                        'priceTrend': 'stable',
                        'percentChange': 0,
                        'isIncreasing': False,
                        'isDecreasing': False,
                        'lastSaleDate': last_sale.get('orderDate') if last_sale else None,
                        'lastSalePrice': last_sale_price,
                        'isFirstVariant': not variants_added,
                        'currencySymbol': get_currency_symbol(user_currency) if username else '$'
                    })
                    processed_results.append(variant_item)
                    variants_added = True

            # If still no variants found, create a default entry
            if not variants_added:
                variant_item = base_item.copy()
                variant_item.update({
                    'variantName': 'Standard',
                    'variantVolume': item.get('volume', 0),
                    'variantCount': 0,
                    'averagePrice': 0,
                    'medianPrice': 0,
                    'lowestPrice': 0,
                    'highestPrice': 0,
                    'priceTrend': 'stable',
                    'percentChange': 0,
                    'isIncreasing': False,
                    'isDecreasing': False,
                    'lastSaleDate': None,
                    'lastSalePrice': 0,
                    'isFirstVariant': True,
                    'currencySymbol': get_currency_symbol(user_currency) if username else '$'
                })
                processed_results.append(variant_item)

        logger.info(f"Retrieved {len(processed_results)} top sales records for game: {game_filter or 'All Games'}")
        return processed_results

    except Exception as e:
        logger.error(f"Error getting top sales data: {str(e)}")
        logger.error(traceback.format_exc())
        return []

@cache.memoize(timeout=21600)  # Cache for 6 hours (21600 seconds)
def get_distinct_game_names():
    """
    Get distinct game names from saleshistories collection that have sales in last 24 hours
    Results are cached for 6 hours
    
    Improved version with optimized MongoDB aggregation pipeline
    """
    logger.info("Fetching distinct TCGPlayer game names with sales")
    try:
        # Optimized aggregation pipeline to get distinct game names with sales
        pipeline = [
            # Match records with sales data in last 24 hours - using index-friendly query
            {
                "$match": {
                    "salesStatistics.last24Hours.total": {"$gt": 0},
                    "gameName": {"$type": "string", "$ne": ""}
                }
            },
            # Use more efficient $group operation with $sum accumulator
            {
                "$group": {
                    "_id": "$gameName",
                    "totalSales": {"$sum": "$salesStatistics.last24Hours.total"}
                }
            },
            # Sort by total sales descending
            {
                "$sort": {"totalSales": -1}
            },
            # Project just the game name
            {
                "$project": {
                    "_id": 0,
                    "gameName": "$_id"
                }
            }
        ]

        # Execute the aggregation pipeline with allowDiskUse for large datasets
        results = list(saleshistories_collection.aggregate(pipeline, allowDiskUse=True))
        game_names = [result["gameName"] for result in results]

        logger.info(f"Retrieved {len(game_names)} distinct game names with sales")
        return game_names

    except Exception as e:
        logger.error(f"Error getting distinct game names: {str(e)}")
        logger.error(traceback.format_exc())
        return []

def get_user_product_data(username, product_id):
    """
    Get user's inventory and price data for a specific product
    Returns a dictionary with inventory and price
    """
    try:
        # Find the product in the user's shProducts collection
        product = shProducts_collection.find_one({
            "username": username,
            "productId": product_id
        })
        
        if not product:
            return {"inventory": 0, "price": None}
        
        # Calculate total inventory across all variants with inventory > 0
        total_inventory = 0
        lowest_price = None
        
        if "variants" in product and isinstance(product["variants"], list):
            for variant in product["variants"]:
                inventory_qty = variant.get("inventory_quantity", 0)
                if inventory_qty > 0:
                    total_inventory += inventory_qty
                
                # Track the lowest price
                price = variant.get("price")
                if price and (lowest_price is None or float(price) < float(lowest_price)):
                    lowest_price = price
        
        return {
            "inventory": total_inventory,
            "price": lowest_price
        }
    except Exception as e:
        logger.error(f"Error getting user product data: {str(e)}")
        logger.error(traceback.format_exc())
        return {"inventory": 0, "price": None}

def batch_get_user_product_data(username, product_ids):
    """
    Get user's inventory and price data for multiple products in a single query
    Returns a dictionary mapping product_id to inventory and price data
    
    This is more efficient than making individual queries for each product
    """
    try:
        # Find all products in the user's shProducts collection that match the product_ids
        products = shProducts_collection.find({
            "username": username,
            "productId": {"$in": product_ids}
        })
        
        result = {}
        
        for product in products:
            product_id = product.get("productId")
            if not product_id:
                continue
                
            # Calculate total inventory across all variants with inventory > 0
            total_inventory = 0
            lowest_price = None
            
            if "variants" in product and isinstance(product["variants"], list):
                for variant in product["variants"]:
                    inventory_qty = variant.get("inventory_quantity", 0)
                    if inventory_qty > 0:
                        total_inventory += inventory_qty
                    
                    # Track the lowest price
                    price = variant.get("price")
                    if price and (lowest_price is None or float(price) < float(lowest_price)):
                        lowest_price = price
            
            result[product_id] = {
                "inventory": total_inventory,
                "price": lowest_price
            }
        
        # Add empty entries for any product_ids that weren't found
        for product_id in product_ids:
            if product_id not in result:
                result[product_id] = {"inventory": 0, "price": None}
        
        return result
    except Exception as e:
        logger.error(f"Error batch getting user product data: {str(e)}")
        logger.error(traceback.format_exc())
        # Return empty data for all requested product_ids
        return {product_id: {"inventory": 0, "price": None} for product_id in product_ids}

@dashboard_bp.route('/dashboard')
@login_required
@subscription_required
@check_ebay_token
def dashboard():
    username = current_user.username
    logger.info(f"Generating dashboard for user: {username}")


    try:
        # Get cached user data (refreshed every 5 minutes)
        five_min_timestamp = datetime.now().replace(second=0, microsecond=0).timestamp() // 300
        user_data = get_cached_user_data(username, five_min_timestamp)

        # Get cached counts (refreshed every minute)
        one_min_timestamp = datetime.now().replace(second=0, microsecond=0).timestamp() // 60
        counts = get_cached_counts(username, one_min_timestamp)

        # Count open tickets
        is_admin = hasattr(current_user, 'roles') and 'Admin' in current_user.roles
        open_tickets_count = count_open_tickets(username, is_admin)

        # Get top 10 sales data with user's inventory and price data
        # Force currency conversion to ensure all prices are in user's currency
        top_sales_data = get_top_sales_data(username=username, force_currency_conversion=True)

        # Get user's subscription status using User model
        user_obj = User.objects(username=username).first()
        if not user_obj:
            logger.error(f"User not found: {username}")
            return render_template('error.html', error_message="User not found")

        try:
            # Get subscription name using the model's method
            subscription_name = user_obj.get_subscription_name()
            logger.info(f"User {username} subscription: {subscription_name}")

            # Only Free users should see the free dashboard
            is_free_user = subscription_name == 'Free'

            # Ensure subscription is properly set on user object
            if subscription_name == 'Lifetime':
                try:
                    # Check if user already has a valid Lifetime subscription
                    if not user_obj.subscription or user_obj.subscription.name != 'Lifetime':
                        # Check if a Lifetime subscription exists in the database
                        subscription = Subscription.objects(name='Lifetime').first()
                        if not subscription:
                            subscription = Subscription(name='Lifetime').save()
                            logger.info(f"Created new Lifetime subscription for user {username}")
                        user_obj.subscription = subscription
                        user_obj.free_subscription = None
                        user_obj.save()
                        logger.info(f"Updated Lifetime subscription for user {username}")
                    else:
                        # Verify the subscription reference is valid
                        try:
                            subscription = Subscription.objects.get(id=user_obj.subscription.id)
                            if subscription.name != 'Lifetime':
                                # Fix incorrect subscription type
                                subscription = Subscription(name='Lifetime').save()
                                user_obj.subscription = subscription
                                user_obj.free_subscription = None
                                user_obj.save()
                                logger.info(f"Fixed incorrect subscription type for user {username}")
                        except DoesNotExist:
                            # Subscription reference is invalid, create new one
                            subscription = Subscription(name='Lifetime').save()
                            user_obj.subscription = subscription
                            user_obj.free_subscription = None
                            user_obj.save()
                            logger.info(f"Fixed invalid subscription reference for user {username}")
                except DoesNotExist:
                    # If there was an issue with the subscription reference, create a new one
                    subscription = Subscription(name='Lifetime').save()
                    user_obj.subscription = subscription
                    user_obj.free_subscription = None
                    user_obj.save()
                    logger.info(f"Created new Lifetime subscription for user {username}")
                except Exception as e:
                    logger.error(f"Error handling Lifetime subscription for user {username}: {str(e)}")
                    logger.error(traceback.format_exc())
                    # Ensure user has at least a free subscription
                    user_obj.subscription = None
                    user_obj.free_subscription = {'start_date': datetime.utcnow()}
                    user_obj.save()
                    logger.info(f"Fallback to free subscription for user {username} after error")
            elif subscription_name == 'Free':
                # Ensure Free users have proper free_subscription data
                if not user_obj.free_subscription:
                    user_obj.subscription = None
                    user_obj.free_subscription = {'start_date': datetime.utcnow()}
                    user_obj.save()
                    logger.info(f"Set up free subscription for user {username}")
                elif user_obj.subscription:
                    # Clear any lingering subscription reference for Free users
                    user_obj.subscription = None
                    user_obj.save()
                    logger.info(f"Cleared lingering subscription for free user {username}")
        except Exception as e:
            logger.error(f"Error handling subscription for user {username}: {str(e)}")
            logger.error(traceback.format_exc())
            # Ensure user has at least a free subscription on error
            try:
                user_obj.subscription = None
                user_obj.free_subscription = {'start_date': datetime.utcnow()}
                user_obj.save()
                logger.info(f"Set up fallback free subscription for user {username} after error")
            except Exception as save_error:
                logger.error(f"Error saving fallback free subscription for user {username}: {str(save_error)}")
                logger.error(traceback.format_exc())

        return render_template(
            'dashboard.html',
            pending_buylist_orders=counts['pending_buylist_orders'],
            staged_count=counts['staged_count'],
            mobile_uploads_count=counts['mobile_uploads_count'],
            shopify_connected=user_data['shopify_connected'],
            auto_update_status=user_data['auto_update_status'],
            auto_pricing_status=user_data['auto_pricing_status'],
            current_user=user_obj,  # Pass the User model instance to the template
            is_free_user=is_free_user,  # Pass the free user status explicitly
            open_tickets_count=open_tickets_count,  # Pass the open tickets count
            top_sales_data=top_sales_data  # Pass the top sales data
        )

    except Exception as e:
        logger.error(f"Error in dashboard route: {str(e)}")
        logger.error(traceback.format_exc())
        return render_template('error.html', error_message="An unexpected error occurred while loading the dashboard.")

@dashboard_bp.route('/api/dashboard/top-sales')
@login_required
@subscription_required
def api_top_sales():
    """
    API endpoint to get top 10 sales data
    Supports optional game filter via query parameter
    Results are cached for 6 hours
    """
    try:
        username = current_user.username
        game_filter = request.args.get('game', None)
        # Force currency conversion to ensure all prices are in user's currency
        top_sales_data = get_top_sales_data(game_filter, username=username, force_currency_conversion=True)
        return jsonify({
            'success': True,
            'data': top_sales_data,
            'count': len(top_sales_data),
            'game_filter': game_filter
        })
    except Exception as e:
        logger.error(f"Error in top sales API: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': 'Failed to retrieve top sales data'
        }), 500

@dashboard_bp.route('/api/dashboard/game-names')
@login_required
@subscription_required
def api_game_names():
    """
    API endpoint to get distinct game names with sales in last 24 hours
    Results are cached for 6 hours
    """
    try:
        game_names = get_distinct_game_names()
        return jsonify({
            'success': True,
            'data': game_names,
            'count': len(game_names)
        })
    except Exception as e:
        logger.error(f"Error in game names API: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': 'Failed to retrieve game names'
        }), 500

@dashboard_bp.route('/comics_scanning')
@login_required
@subscription_required
def comics_scanning():
    return render_template('comics_scanning.html')

@dashboard_bp.route('/sports_card_scanning')
@login_required
@subscription_required
def sports_card_scanning():
    return render_template('sports_card_scanning.html')

@dashboard_bp.route('/csv')
@login_required
@subscription_required
def csv_route():
    return render_template('csv.html')

@dashboard_bp.route('/shopify_autopricing')
@login_required
@subscription_required
def shopify_autopricing():
    return render_template('shopify_autopricing.html')

@dashboard_bp.route('/eu_purchasing')
@login_required
@subscription_required
def eu_purchasing():
    return render_template('eu_purchasing.html')



def identify_tcgplayer_id_column(reader):
    for field in reader.fieldnames:
        if all(re.match(r'^\d+$', str(row[field]).strip()) for row in reader if row[field].strip() and row[field].strip().lower() != 'unavailable'):
            return field
    return None

@dashboard_bp.route('/pos/convert_to_quicklist', methods=['POST'])
@login_required
@subscription_required
def convert_to_quicklist():
    if 'csv' not in request.files:
        logger.error('No file part')
        return jsonify({"error": "No file part"}), 400

    file = request.files['csv']
    logger.debug('Received file for conversion')

    try:
        file_contents = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
        reader = csv.DictReader(file_contents)

        tcgplayer_id_column = identify_tcgplayer_id_column(reader)
        if not tcgplayer_id_column:
            logger.error('No suitable TCGplayer Id column found in the CSV file')
            return jsonify({"error": "No suitable TCGplayer Id column found in the CSV file"}), 400

        logger.info(f'Identified TCGplayer Id column: {tcgplayer_id_column}')

        output = io.StringIO()
        writer = csv.writer(output)

        combined_items = defaultdict(int)

        file_contents.seek(0)  # Reset the reader to the beginning of the file
        reader = csv.DictReader(file_contents)

        for row in reader:
            tcgplayer_id = row[tcgplayer_id_column].strip()
            if tcgplayer_id.isdigit():  # Only process rows with numeric TCGplayer Ids
                tcgplayer_id = int(tcgplayer_id)  # Convert to integer
                # Updated MongoDB query
                product = catalog_collection.find_one(
                    {"skus.skuId": int(tcgplayer_id)},
                    {"categoryId": 1}
                )

                if product and 'categoryId' in product:
                    category_id = product['categoryId']
                    combined_items[(category_id, tcgplayer_id)] += 1
                else:
                    logger.warning(f'No categoryId found for TCGplayer Id: {tcgplayer_id}')

        # Write the data without headers, maintaining the order
        for (category_id, tcgplayer_id), quantity in combined_items.items():
            writer.writerow([category_id, tcgplayer_id, quantity])

        output.seek(0)
        logger.debug('Conversion completed')

        return send_file(
            io.BytesIO(output.getvalue().encode()),
            mimetype='text/csv',
            as_attachment=True,
            download_name='quicklist.csv'
        )
    except Exception as e:
        logger.error(f'Error processing file: {e}')
        logger.error(traceback.format_exc())
        return jsonify({"error": f"Error processing file: {e}"}), 500

@dashboard_bp.route('/biggest_gainers')
@login_required
@subscription_required
def biggest_gainers():
    return render_template('biggest_gainers.html')

@dashboard_bp.route('/sales_summary')
@login_required
@subscription_required
def sales_summary():
    return render_template('sales_summary.html')

@dashboard_bp.route('/setup')
@login_required
@subscription_required
def setup_dashboard():
    """
    Setup dashboard route with options for Profile, Integrations, Pricing, and Buylist
    """
    username = current_user.username
    logger.info(f"Generating setup dashboard for user: {username}")
    
    try:
        # Get user's subscription status
        user_obj = User.objects(username=username).first()
        if not user_obj:
            logger.error(f"User not found: {username}")
            return render_template('error.html', error_message="User not found")
            
        # Get subscription name
        subscription_name = user_obj.get_subscription_name()
        is_free_user = subscription_name == 'Free'
        
        return render_template(
            'setup_dashboard.html',
            current_user=user_obj,
            is_free_user=is_free_user
        )
    except Exception as e:
        logger.error(f"Error in setup dashboard route: {str(e)}")
        logger.error(traceback.format_exc())
        return render_template('error.html', error_message="An unexpected error occurred while loading the setup dashboard.")

@dashboard_bp.route('/dashboard/chat', methods=['POST'])
@login_required
@subscription_required
def chat():
    username = current_user.username
    user_input = request.json.get('message', '')

    logger.info(f"Received chat request from user: {username}")

    # Get yesterday's date range
    yesterday = datetime.utcnow() - timedelta(days=1)
    start_yesterday = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
    end_yesterday = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)

    # Prepare the messages for the OpenAI API
    messages = [
        {"role": "system", "content": f"""You are an AI assistant that helps generate MongoDB queries based on user questions about their order data.
        Always return a valid Python dictionary or list of dictionaries for aggregation pipelines.
        Use ISO format strings for date comparisons, not datetime objects or ISODate.
        Wrap your response in ```python ``` code blocks.
        Do not include any explanations outside the code blocks.
        Always include the username: '{username}' in the query to filter results for this specific user."""},
        {"role": "user", "content": f"""
Generate a MongoDB query to answer the following question about the user's order data:
User question: "{user_input}"
Username: {username}
Yesterday's date range: {start_yesterday.isoformat()} to {end_yesterday.isoformat()}

Remember to wrap your response in ```python ``` code blocks and only include the query, no explanations.
Use ISO format strings for date comparisons, not datetime objects or ISODate.
Ensure the query filters for the specific username: '{username}'.
"""}
    ]

    try:
        # Generate MongoDB query using OpenAI
        response = openai.Completion.create(
            engine="text-davinci-003",
            prompt="\n".join([f"{msg['role']}: {msg['content']}" for msg in messages]),
            max_tokens=500,
            temperature=0.7
        )

        # Parse the AI-generated query
        ai_response = response.choices[0].text.strip()
        # Extract the Python code from the response
        code_match = re.search(r'```python\s*(.*?)\s*```', ai_response, re.DOTALL)
        if code_match:
            query_str = code_match.group(1)
        else:
            query_str = ai_response

        # Remove any leading/trailing whitespace and any print statements
        query_str = re.sub(r'^print\s*\(.*?\)$', '', query_str, flags=re.MULTILINE)
        query_str = query_str.strip()

        # Parse the query string manually
        query = parse_query_string(query_str)

        # Ensure the query includes the username filter
        if isinstance(query, dict) and 'username' not in query:
            query['username'] = username
        elif isinstance(query, list):
            if not any('username' in stage for stage in query if isinstance(stage, dict)):
                query.insert(0, {'$match': {'username': username}})

        logger.info(f"Final query generated for user: {username}")

    except Exception as e:
        logger.error(f"Error generating or parsing query: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({"response": "I'm sorry, I couldn't generate a valid query. Could you please rephrase your question?"})

    # Execute the query
    try:
        if isinstance(query, list):  # Aggregation pipeline
            result = list(shOrders_collection.aggregate(query))
        else:  # Simple find query
            result = list(shOrders_collection.find(query))

        logger.debug(f"Query result: {result}")

        # Convert result to JSON-serializable format
        result_json = json.loads(json_util.dumps(result))

        # Use OpenAI to formulate a human-readable response
        result_messages = [
            {"role": "system", "content": "You are an AI assistant that helps interpret MongoDB query results and formulate clear, concise responses."},
            {"role": "user", "content": f"""
Given the user question "{user_input}" and the query result {result_json},
formulate a clear and concise response in natural language. If the result is empty, mention that no data was found for the given criteria.
"""}
        ]

        response = openai.Completion.create(
            engine="text-davinci-003",
            prompt="\n".join([f"{msg['role']}: {msg['content']}" for msg in result_messages]),
            max_tokens=500,
            temperature=0.7
        )

        return jsonify({"response": response.choices[0].text.strip()})

    except Exception as e:
        logger.error(f"Error executing query or generating response: {str(e)}")
        return jsonify({"response": f"I encountered an error while processing your request: {str(e)}"})

def parse_query_string(query_str):
    # Remove any trailing commas inside brackets or braces
    query_str = re.sub(r',(\s*[\]}])', r'\1', query_str)

    # Replace single quotes with double quotes for JSON compatibility
    query_str = query_str.replace("'", '"')

    # Remove any leading or trailing whitespace
    query_str = query_str.strip()

    # If the string starts with '[', assume it's a pipeline
    if query_str.startswith('['):
        try:
            return json.loads(query_str)
        except json.JSONDecodeError:
            pass  # If it fails, we'll try other methods

    # If the string starts with '{', assume it's a single query
    if query_str.startswith('{'):
        try:
            return json.loads(query_str)
        except json.JSONDecodeError:
            pass  # If it fails, we'll try other methods

    # If we reach here, try to parse it as a Python literal
    try:
        return ast.literal_eval(query_str)
    except (ValueError, SyntaxError):
        pass  # If it fails, we'll try one last method

    # As a last resort, try to evaluate it as Python code
    try:
        # This is potentially dangerous and should be used with caution
        local_vars = {}
        exec(f"query = {query_str}", globals(), local_vars)
        return local_vars['query']
    except Exception as e:
        logger.error(f"Failed to parse query string: {e}")
        logger.error(f"Problematic query string: {query_str}")
        raise ValueError("Could not parse query string")

def convert_datetime(obj):
    if isinstance(obj, dict):
        return {k: convert_datetime(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_datetime(i) for i in obj]
    elif isinstance(obj, str) and obj.startswith('datetime'):
        return eval(obj)
    return obj

def parse_date_string(date_string):
    """
    Parse a date string into a datetime object.
    Supports various formats including ISO format and common date strings.
    """
    try:
        return datetime.fromisoformat(date_string)
    except ValueError:
        try:
            return datetime.strptime(date_string, "%Y-%m-%d")
        except ValueError:
            try:
                return datetime.strptime(date_string, "%m/%d/%Y")
            except ValueError:
                raise ValueError(f"Unable to parse date string: {date_string}")

def get_date_range(date_string):
    """
    Get a date range for a given date string.
    Supports 'today', 'yesterday', 'this week', 'last week', 'this month', 'last month'.
    Returns a tuple of (start_date, end_date).
    """
    today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    if date_string == 'today':
        return today, today.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif date_string == 'yesterday':
        yesterday = today - timedelta(days=1)
        return yesterday, yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif date_string == 'this week':
        start_of_week = today - timedelta(days=today.weekday())
        end_of_week = start_of_week + timedelta(days=6, hours=23, minutes=59, seconds=59, microseconds=999999)
        return start_of_week, end_of_week
