from flask import Blueprint, render_template, request, jsonify, current_app, abort
from flask_login import login_required, current_user
from datetime import datetime
import json
import shopify

from models.event_model import Event
from models.attendee_model import Attendee
from utils.game_utils import get_distinct_games
from utils.shopify_event_utils import create_event_ticket_product, update_event_ticket_product, delete_event_ticket_product, generate_recurrence_dates, get_shopify_credentials

events = Blueprint('events', __name__)

@events.route('/events-dashboard')
@login_required
def events_dashboard():
    """
    Render the events dashboard page with event statistics and upcoming events.
    """
    # Get statistics
    stats = {
        'upcoming': Event.objects(created_by=current_user.username, status='upcoming').count(),
        'active': Event.objects(created_by=current_user.username, status='active').count(),
        'total_attendees': 0,  # Will be calculated
        'total_revenue': 0.0   # Will be calculated
    }

    # Calculate total attendees and revenue
    all_events = Event.objects(created_by=current_user.username)
    for event in all_events:
        # For now, just use max_attendees as a placeholder for actual attendees
        stats['total_attendees'] += event.max_attendees
        stats['total_revenue'] += event.ticket_price * event.max_attendees

    # Get upcoming events (limit to 5)
    upcoming_events = Event.objects(
        created_by=current_user.username,
        status='upcoming',
        start_datetime__gte=datetime.now()
    ).order_by('start_datetime').limit(5)

    return render_template(
        'events-dashboard.html',
        stats=stats,
        upcoming_events=upcoming_events
    )

@events.route('/create-events', methods=['GET', 'POST'])
@login_required
def create_events():
    """
    Handle event creation form.
    """
    if request.method == 'POST':
        try:
            # Check if user has Shopify credentials for paid events
            ticket_price = float(request.form.get('ticket_price', 0))
            if ticket_price > 0:
                try:
                    get_shopify_credentials(current_user.username)
                except ValueError as e:
                    current_app.logger.warning(f"User {current_user.username} attempted to create paid event without Shopify credentials: {str(e)}")
                    return jsonify({
                        "success": False,
                        "message": f"Shopify integration required for paid events: {str(e)}"
                    }), 400

            # Extract form data
            form_data = request.form

            # Parse date and time
            event_date = form_data.get('event_date')
            event_time = form_data.get('event_time')
            if not event_date or not event_time:
                return jsonify({"success": False, "message": "Date and time are required"}), 400

            start_datetime = datetime.strptime(f"{event_date} {event_time}", "%Y-%m-%d %H:%M")

            # Handle game selection
            game = form_data.get('game')
            custom_game = game == 'custom'
            custom_game_name = form_data.get('custom_game_name') if custom_game else None

            # Create parent event
            parent_event = Event(
                title=form_data.get('title'),
                description=form_data.get('description'),
                game=None if custom_game else game,
                custom_game=custom_game,
                custom_game_name=custom_game_name,
                start_datetime=start_datetime,
                max_attendees=int(form_data.get('max_attendees')),
                ticket_price=float(form_data.get('ticket_price')),
                cost_per_attendee=float(form_data.get('cost_per_attendee', 0)),
                event_type=form_data.get('event_type'),
                prize_details=form_data.get('prize_details', ''),
                created_by=current_user.username
            )

            # Check if it's a recurring event
            is_recurring = form_data.get('is_recurring') == 'on'

            if is_recurring:
                parent_event.is_recurring = True
                parent_event.is_recurrence_parent = True
                parent_event.recurrence_type = form_data.get('recurrence_type')

                # Build recurrence details based on type
                recurrence_details = {}

                if parent_event.recurrence_type == 'weekly':
                    # Get selected days of week
                    days = request.form.getlist('weekly_days')
                    days = [int(day) for day in days]
                    recurrence_details['days'] = days
                    recurrence_details['weeks'] = form_data.get('weekly_weeks', '12')  # Default to 12 weeks

                elif parent_event.recurrence_type == 'monthly':
                    monthly_option = form_data.get('monthly_option')
                    recurrence_details['type'] = monthly_option
                    recurrence_details['months'] = form_data.get('monthly_months', '6')  # Default to 6 months

                    if monthly_option == 'day_of_week':
                        recurrence_details['week_number'] = form_data.get('week_number')
                        recurrence_details['day_of_week'] = form_data.get('day_of_week')

                elif parent_event.recurrence_type == 'custom':
                    # Get custom dates
                    custom_dates = request.form.getlist('custom_dates[]')
                    recurrence_details['dates'] = custom_dates

                parent_event.recurrence_details = recurrence_details

                # Save parent record first
                parent_event.save()

                # Generate all recurrence dates
                recurrence_dates = generate_recurrence_dates(
                    start_datetime,
                    parent_event.recurrence_type,
                    recurrence_details
                )

                # Create child events for each date
                child_events = []
                for date in recurrence_dates:
                    # Create child event instance (copy most fields from parent)
                    child_event = Event(
                        title=parent_event.title,
                        description=parent_event.description,
                        game=parent_event.game,
                        custom_game=parent_event.custom_game,
                        custom_game_name=parent_event.custom_game_name,
                        start_datetime=date,
                        max_attendees=parent_event.max_attendees,
                        ticket_price=parent_event.ticket_price,
                        cost_per_attendee=parent_event.cost_per_attendee,
                        event_type=parent_event.event_type,
                        prize_details=parent_event.prize_details,
                        created_by=parent_event.created_by,
                        parent_event_id=str(parent_event.id),
                        is_recurring=False  # Child events are not recurring themselves
                    )

                    # Save child event
                    child_event.save()

                    # Create Shopify product for this instance
                    try:
                        success, result = create_event_ticket_product(child_event, current_user.username)

                        if success:
                            # Update child event with Shopify details
                            child_event.shopify_product_id = result['product_id']
                            child_event.shopify_variant_id = result['variant_id']
                            child_event.shopify_product_url = result['product_url']
                            child_event.save()

                            # Add child ID to parent's instances list
                            parent_event.recurrence_instances.append(str(child_event.id))
                            child_events.append(child_event)
                        else:
                            # Check if we have a product ID in the result (partial success)
                            if isinstance(result, dict) and 'product_id' in result:
                                # We have a product ID, so we can still save the event
                                child_event.shopify_product_id = result['product_id']
                                if 'variant_id' in result:
                                    child_event.shopify_variant_id = result['variant_id']
                                if 'product_url' in result:
                                    child_event.shopify_product_url = result['product_url']
                                child_event.save()

                                # Add child ID to parent's instances list
                                parent_event.recurrence_instances.append(str(child_event.id))
                                child_events.append(child_event)

                                # Log warning but continue
                                current_app.logger.warning(f"Partial success creating Shopify product for event {child_event.id}: {result}")
                            else:
                                # Log error but continue with other events
                                current_app.logger.error(f"Error creating Shopify product for event {child_event.id}: {result}")
                    except Exception as e:
                        # Log exception but continue with other events
                        current_app.logger.error(f"Exception creating Shopify product for event {child_event.id}: {str(e)}")

                # Update parent with list of child IDs
                parent_event.save()

                return jsonify({
                    "success": True,
                    "message": f"Created recurring event with {len(child_events)} instances",
                    "event_id": str(parent_event.id)
                })
            else:
                # Single event - create and save
                parent_event.save()

                # Create Shopify product
                try:
                    current_app.logger.info(f"Creating Shopify product for user: {current_user.username}")
                    success, result = create_event_ticket_product(parent_event, current_user.username)

                    if success:
                        # Update event with Shopify details
                        parent_event.shopify_product_id = result['product_id']
                        parent_event.shopify_variant_id = result['variant_id']
                        parent_event.shopify_product_url = result['product_url']
                        parent_event.save()

                        current_app.logger.info(f"Successfully created Shopify product {result['product_id']} for user {current_user.username}")
                        return jsonify({
                            "success": True,
                            "message": "Event created successfully",
                            "event_id": str(parent_event.id)
                        })
                    else:
                        # Log the error but don't fail the request
                        current_app.logger.error(f"Error creating Shopify product for user {current_user.username}: {result}")

                        # Check if we have a product ID in the result (partial success)
                        if isinstance(result, dict) and 'product_id' in result:
                            # We have a product ID, so we can still save the event
                            parent_event.shopify_product_id = result['product_id']
                            if 'variant_id' in result:
                                parent_event.shopify_variant_id = result['variant_id']
                            if 'product_url' in result:
                                parent_event.shopify_product_url = result['product_url']
                            parent_event.save()

                            return jsonify({
                                "success": True,
                                "message": "Event created successfully (with warnings)",
                                "event_id": str(parent_event.id),
                                "warning": f"Warning: {result.get('warning', 'Some features may not work correctly')}"
                            })
                        else:
                            # Complete failure
                            return jsonify({
                                "success": False,
                                "message": f"Error creating Shopify product: {result}"
                            }), 500
                except Exception as e:
                    current_app.logger.error(f"Exception in create_event_ticket_product for user {current_user.username}: {str(e)}")
                    return jsonify({
                        "success": False,
                        "message": f"Error creating Shopify product: {str(e)}"
                    }), 500

        except Exception as e:
            current_app.logger.error(f"Error creating event: {str(e)}")
            return jsonify({"success": False, "message": f"Error creating event: {str(e)}"}), 500

    # For GET request, fetch game list from collection catalog
    games = get_distinct_games()
    return render_template('events/create-events.html', games=games)

@events.route('/api/games', methods=['GET'])
@login_required
def get_games():
    """
    API endpoint to fetch distinct game names for the dropdown.
    """
    games = get_distinct_games()
    return jsonify(games)

@events.route('/view-events')
@login_required
def view_events():
    """
    Render the view events page.
    """
    # Get all events for the current user
    events_list = Event.objects(created_by=current_user.username).order_by('-start_datetime')

    # Add attendee count to each event
    for event in events_list:
        event.attendee_count = Attendee.objects(event=event).count()

    return render_template('events/view-events.html', events=events_list)

@events.route('/edit-event/<event_id>', methods=['GET', 'POST'])
@login_required
def edit_event(event_id):
    """
    Handle event editing.
    """
    # Find the event
    event = Event.objects(id=event_id, created_by=current_user.username).first()
    if not event:
        abort(404)

    if request.method == 'POST':
        try:
            # Extract form data
            form_data = request.form

            # Parse date and time
            event_date = form_data.get('event_date')
            event_time = form_data.get('event_time')
            if not event_date or not event_time:
                return jsonify({"success": False, "message": "Date and time are required"}), 400

            start_datetime = datetime.strptime(f"{event_date} {event_time}", "%Y-%m-%d %H:%M")

            # Handle game selection
            game = form_data.get('game')
            custom_game = game == 'custom'
            custom_game_name = form_data.get('custom_game_name') if custom_game else None

            # Update event fields
            event.title = form_data.get('title')
            event.description = form_data.get('description')
            event.game = None if custom_game else game
            event.custom_game = custom_game
            event.custom_game_name = custom_game_name
            event.start_datetime = start_datetime
            event.max_attendees = int(form_data.get('max_attendees'))
            event.ticket_price = float(form_data.get('ticket_price'))
            event.cost_per_attendee = float(form_data.get('cost_per_attendee', 0))
            event.event_type = form_data.get('event_type')
            event.prize_details = form_data.get('prize_details', '')

            # Save the updated event
            event.save()

            # Update Shopify product if it exists
            if event.shopify_product_id:
                try:
                    success, result = update_event_ticket_product(event, current_user.username)

                    if success:
                        # Update event with any updated Shopify details
                        event.shopify_product_url = result['product_url']
                        event.save()

                        return jsonify({
                            "success": True,
                            "message": "Event updated successfully",
                            "event_id": str(event.id)
                        })
                    else:
                        # Check if we have a product ID in the result (partial success)
                        if isinstance(result, dict) and 'product_id' in result:
                            # We have a product ID, so we can still save the event
                            if 'product_url' in result:
                                event.shopify_product_url = result['product_url']
                            event.save()

                            return jsonify({
                                "success": True,
                                "message": "Event updated successfully (with warnings)",
                                "event_id": str(event.id),
                                "warning": f"Warning: {result.get('warning', 'Some features may not work correctly')}"
                            })
                        else:
                            # Log the error but don't fail the request
                            current_app.logger.error(f"Error updating Shopify product: {result}")

                            # Return success anyway since the event itself was updated
                            return jsonify({
                                "success": True,
                                "message": "Event updated successfully (Shopify product update failed)",
                                "event_id": str(event.id),
                                "warning": f"Warning: Failed to update Shopify product: {result}"
                            })
                except Exception as e:
                    current_app.logger.error(f"Exception in update_event_ticket_product: {str(e)}")

                    # Return success anyway since the event itself was updated
                    return jsonify({
                        "success": True,
                        "message": "Event updated successfully (Shopify product update failed)",
                        "event_id": str(event.id),
                        "warning": f"Warning: Exception updating Shopify product: {str(e)}"
                    })
            else:
                return jsonify({
                    "success": True,
                    "message": "Event updated successfully (no Shopify product)",
                    "event_id": str(event.id)
                })

        except Exception as e:
            current_app.logger.error(f"Error updating event: {str(e)}")
            return jsonify({"success": False, "message": f"Error updating event: {str(e)}"}), 500

    # For GET request, fetch game list and render edit form
    games = get_distinct_games()
    return render_template('events/edit-event.html', event=event, games=games)

@events.route('/delete-event/<event_id>', methods=['POST'])
@login_required
def delete_event(event_id):
    """
    Handle event deletion.
    """
    # Find the event
    event = Event.objects(id=event_id, created_by=current_user.username).first()
    if not event:
        abort(404)

    try:
        # Delete Shopify product if it exists
        if event.shopify_product_id:
            success, result = delete_event_ticket_product(event, current_user.username)

            if not success:
                current_app.logger.error(f"Error deleting Shopify product: {result}")
                # Continue with event deletion even if Shopify deletion fails

        # If this is a parent event, delete all child events
        if event.is_recurring and event.is_recurrence_parent and event.recurrence_instances:
            for child_id in event.recurrence_instances:
                child_event = Event.objects(id=child_id).first()
                if child_event:
                    # Delete child event's Shopify product
                    if child_event.shopify_product_id:
                        delete_event_ticket_product(child_event, current_user.username)
                    # Delete child event
                    child_event.delete()

        # Delete the event
        event.delete()

        return jsonify({"success": True, "message": "Event deleted successfully"})

    except Exception as e:
        current_app.logger.error(f"Error deleting event: {str(e)}")
        return jsonify({"success": False, "message": f"Error deleting event: {str(e)}"}), 500

@events.route('/bulk-delete-events', methods=['POST'])
@login_required
def bulk_delete_events():
    """
    Handle bulk event deletion.
    """
    try:
        # Get event IDs from request
        data = request.json
        event_ids = data.get('event_ids', [])

        if not event_ids:
            return jsonify({"success": False, "message": "No events selected for deletion"}), 400

        # Find all events that belong to the current user
        events = Event.objects(id__in=event_ids, created_by=current_user.username)

        # Check if all events were found
        if len(events) != len(event_ids):
            return jsonify({"success": False, "message": "One or more events not found or not authorized"}), 404

        # Track deletion statistics
        deleted_count = 0
        child_count = 0
        shopify_errors = 0

        # Process each event
        for event in events:
            # Delete Shopify product if it exists
            if event.shopify_product_id:
                success, result = delete_event_ticket_product(event, current_user.username)
                if not success:
                    current_app.logger.error(f"Error deleting Shopify product for event {event.id}: {result}")
                    shopify_errors += 1

            # If this is a parent event, delete all child events
            if event.is_recurring and event.is_recurrence_parent and event.recurrence_instances:
                for child_id in event.recurrence_instances:
                    child_event = Event.objects(id=child_id).first()
                    if child_event:
                        # Delete child event's Shopify product
                        if child_event.shopify_product_id:
                            delete_event_ticket_product(child_event, current_user.username)
                        # Delete child event
                        child_event.delete()
                        child_count += 1

            # Delete the event
            event.delete()
            deleted_count += 1

        # Prepare success message
        message = f"Successfully deleted {deleted_count} events"
        if child_count > 0:
            message += f" and {child_count} recurring instances"
        if shopify_errors > 0:
            message += f" (with {shopify_errors} Shopify errors)"

        return jsonify({"success": True, "message": message})

    except Exception as e:
        current_app.logger.error(f"Error in bulk delete: {str(e)}")
        return jsonify({"success": False, "message": f"Error deleting events: {str(e)}"}), 500

@events.route('/event/<event_id>/attendees')
@login_required
def event_attendees(event_id):
    """
    Render the attendees page for a specific event.
    """
    # Find the event
    event = Event.objects(id=event_id, created_by=current_user.username).first()
    if not event:
        abort(404)

    # Get all attendees for this event
    attendees_list = Attendee.objects(event=event).order_by('full_name')

    return render_template('events/attendees.html', event=event, attendees=attendees_list)

@events.route('/event/<event_id>/attendees/add', methods=['POST'])
@login_required
def add_attendee(event_id):
    """
    Add a new attendee to an event.
    """
    # Find the event
    event = Event.objects(id=event_id, created_by=current_user.username).first()
    if not event:
        return jsonify({"success": False, "message": "Event not found"}), 404

    try:
        # Get form data
        data = request.json

        # Validate required fields
        if not data.get('full_name') or not data.get('email'):
            return jsonify({"success": False, "message": "Full name and email are required"}), 400

        # Check if attendee already exists for this event
        existing_attendee = Attendee.objects(event=event, email=data.get('email')).first()
        if existing_attendee:
            return jsonify({"success": False, "message": "An attendee with this email is already registered for this event"}), 400

        # Get the game name from the event
        game_name = event.custom_game_name if event.custom_game else event.game

        # Create new attendee
        attendee = Attendee(
            full_name=data.get('full_name'),
            email=data.get('email'),
            event=event,
            game_name=game_name,
            registration_source="manual",
            notes=data.get('notes', '')
        )

        # Save the attendee
        attendee.save()

        return jsonify({
            "success": True,
            "message": "Attendee added successfully",
            "attendee": {
                "id": str(attendee.id),
                "full_name": attendee.full_name,
                "email": attendee.email,
                "game_name": attendee.game_name,
                "registration_date": attendee.registration_date.strftime("%Y-%m-%d %H:%M"),
                "checked_in": attendee.checked_in
            }
        })

    except Exception as e:
        current_app.logger.error(f"Error adding attendee: {str(e)}")
        return jsonify({"success": False, "message": f"Error adding attendee: {str(e)}"}), 500

@events.route('/event/<event_id>/attendees/<attendee_id>/check-in', methods=['POST'])
@login_required
def check_in_attendee(event_id, attendee_id):
    """
    Check in an attendee for an event.
    """
    # Find the event
    event = Event.objects(id=event_id, created_by=current_user.username).first()
    if not event:
        return jsonify({"success": False, "message": "Event not found"}), 404

    # Find the attendee
    attendee = Attendee.objects(id=attendee_id, event=event).first()
    if not attendee:
        return jsonify({"success": False, "message": "Attendee not found"}), 404

    try:
        # Check in the attendee
        attendee.check_in()

        return jsonify({
            "success": True,
            "message": "Attendee checked in successfully",
            "checked_in_time": attendee.checked_in_time.strftime("%Y-%m-%d %H:%M")
        })

    except Exception as e:
        current_app.logger.error(f"Error checking in attendee: {str(e)}")
        return jsonify({"success": False, "message": f"Error checking in attendee: {str(e)}"}), 500

@events.route('/event/<event_id>/attendees/<attendee_id>/delete', methods=['POST'])
@login_required
def delete_attendee(event_id, attendee_id):
    """
    Delete an attendee from an event.
    """
    # Find the event
    event = Event.objects(id=event_id, created_by=current_user.username).first()
    if not event:
        return jsonify({"success": False, "message": "Event not found"}), 404

    # Find the attendee
    attendee = Attendee.objects(id=attendee_id, event=event).first()
    if not attendee:
        return jsonify({"success": False, "message": "Attendee not found"}), 404

    try:
        # Delete the attendee
        attendee.delete()

        return jsonify({
            "success": True,
            "message": "Attendee deleted successfully"
        })

    except Exception as e:
        current_app.logger.error(f"Error deleting attendee: {str(e)}")
        return jsonify({"success": False, "message": f"Error deleting attendee: {str(e)}"}), 500

@events.route('/results')
@login_required
def results():
    """
    Render the results page.
    """
    return render_template('events/results.html')

@events.route('/events-calendar')
@login_required
def calendar():
    """
    Render the events list page.
    """
    # Get all events for the current user
    events_list = Event.objects(created_by=current_user.username).order_by('start_datetime')

    # Add attendee count to each event
    for event in events_list:
        event.attendee_count = Attendee.objects(event=event).count()

    return render_template('events/calendar.html', events=events_list)

@events.route('/events/<username>')
def public_events(username):
    """
    Render the public events page for a specific user.
    """
    # Get all public events for the specified user
    events_list = Event.objects(created_by=username, status__in=['upcoming', 'active']).order_by('start_datetime')

    # Add attendee count to each event
    for event in events_list:
        event.attendee_count = Attendee.objects(event=event).count()

    # Prepare events data for FullCalendar
    events_data = []
    for event in events_list:
        game_class = 'game-custom'
        if not event.custom_game and event.game:
            if event.game == 'Magic The Gathering':
                game_class = 'game-magic'
            elif event.game == 'YuGiOh':
                game_class = 'game-yugioh'
            elif event.game == 'Pokemon':
                game_class = 'game-pokemon'
            elif event.game == 'Star Wars':
                game_class = 'game-starwars'
            elif event.game == 'Lorcana':
                game_class = 'game-lorcana'
            elif event.game == 'Digimon':
                game_class = 'game-digimon'
            elif event.game == 'Dragonball':
                game_class = 'game-dragonball'
            elif event.game == 'Sorcery':
                game_class = 'game-sorcery'
            elif event.game == 'Metazoo':
                game_class = 'game-metazoo'
            elif event.game == 'Final Fantasy':
                game_class = 'game-finalfantasy'

        game_name = event.custom_game_name if event.custom_game else event.game

        # Fix Shopify URL if it's an admin URL
        shopify_url = None
        if hasattr(event, 'shopify_product_url') and event.shopify_product_url:
            if '/admin/' in event.shopify_product_url:
                # Get the product ID from the admin URL
                import re
                product_id_match = re.search(r'/products/(\d+)', event.shopify_product_url)
                if product_id_match:
                    product_id = product_id_match.group(1)

                    # Get the product handle from Shopify API
                    try:
                        # Initialize Shopify API session
                        credentials = get_shopify_credentials(event.created_by)
                        session = shopify.Session(credentials['shop_url'], '2023-07', credentials['access_token'])
                        shopify.ShopifyResource.activate_session(session)

                        # Extract store name for frontend URL
                        store_name = credentials['shop_url'].replace('.myshopify.com', '')

                        # Get the product
                        product = shopify.Product.find(product_id)
                        if product:
                            # Use the handle from the API with the correct store domain
                            shopify_url = f"https://{store_name}.myshopify.com/products/{product.handle}"
                        else:
                            # Fallback if product not found
                            shopify_url = f"https://{store_name}.myshopify.com/products/{product_id}"

                        # Clear the session
                        shopify.ShopifyResource.clear_session()
                    except Exception as e:
                        current_app.logger.error(f"Error getting product handle: {str(e)}")
                        # Fallback if API call fails - try to get store name from event creator
                        try:
                            credentials = get_shopify_credentials(event.created_by)
                            store_name = credentials['shop_url'].replace('.myshopify.com', '')
                            shopify_url = f"https://{store_name}.myshopify.com/products/{product_id}"
                        except:
                            shopify_url = f"https://shop.myshopify.com/products/{product_id}"
                else:
                    # Fallback if product ID not found in URL
                    shopify_url = event.shopify_product_url
            else:
                shopify_url = event.shopify_product_url

        # Format the event time and price for display
        event_time = event.start_datetime.strftime('%d/%m/%Y, %H:%M')
        formatted_price = f"£{event.ticket_price:.2f}" if event.ticket_price else "Free"
        availability = f"{event.attendee_count}/{event.max_attendees}"

        events_data.append({
            'id': str(event.id),
            'title': event.title,
            'start': event.start_datetime.isoformat(),
            'className': game_class,
            'extendedProps': {
                'description': event.description,
                'game': game_name,
                'eventType': event.event_type,
                'maxAttendees': event.max_attendees,
                'currentAttendees': event.attendee_count,
                'ticketPrice': event.ticket_price,
                'shopifyProductUrl': shopify_url,
                # Additional properties for tooltip
                'originalTitle': event.title,
                'time': event_time,
                'formattedPrice': formatted_price,
                'availability': availability
            }
        })

    # Track which games have events
    games_with_events = set()
    has_custom_events = False

    for event in events_list:
        if event.custom_game:
            has_custom_events = True
        elif event.game:
            games_with_events.add(event.game)

    # Convert to JSON for the template
    import json
    events_json = json.dumps(events_data)

    return render_template(
        'events/public_events.html',
        events_json=events_json,
        username=username,
        games_with_events=games_with_events,
        has_custom_events=has_custom_events
    )

@events.route('/api/public/register-attendee', methods=['POST'])
def register_public_attendee():
    """
    API endpoint for registering attendees from the public events page.
    """
    try:
        # Get form data
        data = request.json

        # Validate required fields
        if not data.get('event_id') or not data.get('full_name') or not data.get('email'):
            return jsonify({"success": False, "message": "Event ID, full name, and email are required"}), 400

        # Find the event
        event = Event.objects(id=data.get('event_id')).first()
        if not event:
            return jsonify({"success": False, "message": "Event not found"}), 404

        # Check if attendee already exists for this event
        existing_attendee = Attendee.objects(event=event, email=data.get('email')).first()
        if existing_attendee:
            return jsonify({"success": False, "message": "You are already registered for this event"}), 400

        # Check if event is full
        current_attendees = Attendee.objects(event=event).count()
        if current_attendees >= event.max_attendees:
            return jsonify({"success": False, "message": "This event is full"}), 400

        # Get the game name from the event
        game_name = event.custom_game_name if event.custom_game else event.game

        # Create new attendee
        attendee = Attendee(
            full_name=data.get('full_name'),
            email=data.get('email'),
            event=event,
            game_name=game_name,
            registration_source="public_website",
            notes=data.get('notes', ''),
            future_events_opt_in=data.get('future_events', True)
        )

        # Save the attendee
        attendee.save()

        return jsonify({
            "success": True,
            "message": "Registration successful",
            "attendee_id": str(attendee.id)
        })

    except Exception as e:
        current_app.logger.error(f"Error registering attendee: {str(e)}")
        return jsonify({"success": False, "message": f"Error registering attendee: {str(e)}"}), 500
