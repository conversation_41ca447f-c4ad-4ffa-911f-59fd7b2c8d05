{% extends "base.html" %}
{% block title %}Customers Dashboard{% endblock %}
{% block content %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/setup.css') }}">

<div class="container-fluid dashboard-container">
    {% if not current_user.subscription or current_user.subscription.name == 'Free' %}
    <div class="alert alert-primary mt-1 mb-2 py-2 d-flex justify-content-between align-items-center" role="alert" style="font-size: 0.9rem;">
        <div class="text-center w-100">
            <i class="fas fa-star me-1"></i>
            <strong>Upgrade your plan to unlock all features!</strong> Get access to advanced analytics, premium tools, and more.
        </div>
        <a href="{{ url_for('activation.activating') }}" class="btn btn-primary btn-sm py-1 px-2">
            Upgrade Now
        </a>
    </div>
    {% endif %}

    <!-- Customers Dashboard Header -->
    <div class="setup-header">
        <h1><i class="fas fa-users me-2"></i>Customers Dashboard</h1>
        <p class="text-muted">Manage your customers, analytics, and communication</p>
    </div>

    <!-- Customers Options Grid -->
    <div class="setup-grid">
        <!-- Shopify Customers Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fab fa-shopify"></i>
                <h2>Shopify Customers</h2>
            </div>
            <div class="setup-card-body">
                <p>View and manage your Shopify customers. Access customer details, order history, and spending analytics from your Shopify store.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Customer details</li>
                    <li><i class="fas fa-check-circle"></i> Order history</li>
                    <li><i class="fas fa-check-circle"></i> Spending analytics</li>
                    <li><i class="fas fa-check-circle"></i> Contact information</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('shopify_customers.get_customers') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>View Shopify Customers
                </a>
            </div>
        </div>

        <!-- Customer Analytics Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-chart-line"></i>
                <h2>Customer Analytics</h2>
            </div>
            <div class="setup-card-body">
                <p>Advanced customer analytics and insights. Track customer lifetime value, purchase patterns, and segmentation data.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Customer lifetime value</li>
                    <li><i class="fas fa-check-circle"></i> Purchase patterns</li>
                    <li><i class="fas fa-check-circle"></i> Segmentation data</li>
                    <li><i class="fas fa-check-circle"></i> Behavior analysis</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <div class="coming-soon-badge" style="position: absolute; top: 10px; right: 10px; background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.7rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); z-index: 10;">Coming Soon</div>
                <a href="#" class="btn btn-primary" style="pointer-events: none; opacity: 0.6;">
                    <i class="fas fa-arrow-right me-2"></i>View Analytics
                </a>
            </div>
        </div>

        <!-- Customer Segments Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-users"></i>
                <h2>Customer Segments</h2>
            </div>
            <div class="setup-card-body">
                <p>Create and manage customer segments based on purchase behavior, demographics, and engagement levels for targeted marketing.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Behavior-based segments</li>
                    <li><i class="fas fa-check-circle"></i> Demographic grouping</li>
                    <li><i class="fas fa-check-circle"></i> Engagement levels</li>
                    <li><i class="fas fa-check-circle"></i> Targeted marketing</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <div class="coming-soon-badge" style="position: absolute; top: 10px; right: 10px; background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.7rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); z-index: 10;">Coming Soon</div>
                <a href="#" class="btn btn-primary" style="pointer-events: none; opacity: 0.6;">
                    <i class="fas fa-arrow-right me-2"></i>Manage Segments
                </a>
            </div>
        </div>

        <!-- Customer Communication Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-envelope"></i>
                <h2>Customer Communication</h2>
            </div>
            <div class="setup-card-body">
                <p>Send targeted emails, newsletters, and promotional campaigns to your customers. Track engagement and response rates.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Targeted emails</li>
                    <li><i class="fas fa-check-circle"></i> Newsletters</li>
                    <li><i class="fas fa-check-circle"></i> Promotional campaigns</li>
                    <li><i class="fas fa-check-circle"></i> Engagement tracking</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <div class="coming-soon-badge" style="position: absolute; top: 10px; right: 10px; background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.7rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); z-index: 10;">Coming Soon</div>
                <a href="#" class="btn btn-primary" style="pointer-events: none; opacity: 0.6;">
                    <i class="fas fa-arrow-right me-2"></i>Send Communications
                </a>
            </div>
        </div>

        <!-- Store Credit Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-credit-card"></i>
                <h2>Store Credit</h2>
            </div>
            <div class="setup-card-body">
                <p>Manage customer store credit balances, issue refunds as store credit, and track credit usage across all customer transactions.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Credit balances</li>
                    <li><i class="fas fa-check-circle"></i> Issue refunds</li>
                    <li><i class="fas fa-check-circle"></i> Track credit usage</li>
                    <li><i class="fas fa-check-circle"></i> Transaction history</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <div class="coming-soon-badge" style="position: absolute; top: 10px; right: 10px; background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.7rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); z-index: 10;">Coming Soon</div>
                <a href="#" class="btn btn-primary" style="pointer-events: none; opacity: 0.6;">
                    <i class="fas fa-arrow-right me-2"></i>Manage Store Credit
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    /* Setup Dashboard Specific Styles */
    .setup-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .setup-header h1 {
        font-size: 2rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
    }
    
    .setup-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }
    
    .setup-card {
        background: linear-gradient(135deg, rgba(26, 32, 44, 0.8), rgba(45, 55, 72, 0.8));
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.05);
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
    }
    
    .setup-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .setup-card-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .setup-card-header i {
        font-size: 2rem;
        color: var(--primary-color);
    }
    
    .setup-card-header h2 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #ffffff;
        margin: 0;
    }
    
    .setup-card-body {
        padding: 1.5rem;
        flex-grow: 1;
    }
    
    .setup-card-body p {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 1.5rem;
    }
    
    .setup-features-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .setup-features-list li {
        margin-bottom: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .setup-features-list li i {
        color: var(--success-color);
        font-size: 0.9rem;
    }
    
    .setup-card-footer {
        padding: 1.5rem;
        border-top: 1px solid rgba(255, 255, 255, 0.05);
        text-align: center;
    }
    
    .setup-card-footer .btn {
        width: 100%;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .setup-card-footer .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .setup-grid {
            grid-template-columns: 1fr;
        }
        
        .setup-header h1 {
            font-size: 1.75rem;
        }
    }
</style>
{% endblock %}
