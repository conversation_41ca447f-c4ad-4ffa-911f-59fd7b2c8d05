import pymongo
import json
import os
import requests
import logging
import re
import time
from datetime import datetime, timedelta, timezone
from bson import ObjectId

# Setup logging
if not os.path.exists('logs'):
    os.makedirs('logs')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/shopify_inventory_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Hardcoded MongoDB connection details
mongo_uri = "mongodb://admin:Reggie2805!@*************:27017/?authSource=admin"
mongo_client = pymongo.MongoClient(mongo_uri)
db = mongo_client.test

# Collections
skip_warehouse_collection = db['skipWarehouse']
inventory_collection = db['inventory']
shProducts_collection = db['shProducts']
user_collection = db['user']
sync_logs_collection = db['syncLogs']

def log_to_mongodb(username, operation_type, stats, status="success", error=None):
    """
    Log sync operations to MongoDB syncLogs collection
    
    Args:
        username (str): The username associated with the sync
        operation_type (str): Type of operation (e.g., "associate_ids", "push_to_shopify")
        stats (dict): Statistics about the operation (e.g., records processed)
        status (str): Status of the operation ("success", "partial", "failed")
        error (str, optional): Error message if any
    """
    try:
        log_entry = {
            "username": username,
            "operation": operation_type,
            "timestamp": datetime.now(timezone.utc),
            "stats": stats,
            "status": status
        }
        
        if error:
            log_entry["error"] = error
            
        # Insert into syncLogs collection
        sync_logs_collection.insert_one(log_entry)
        logger.info(f"Logged {operation_type} operation for {username} to MongoDB")
    except Exception as e:
        logger.error(f"Failed to log to MongoDB: {str(e)}")

def get_enabled_users():
    """Get all users with skipWarehouse enabled."""
    enabled_users = []
    try:
        # Find all users with skipWarehouse enabled
        cursor = skip_warehouse_collection.find({'enabled': True})
        for doc in cursor:
            enabled_users.append(doc['username'])
        
        logger.info(f"Found {len(enabled_users)} users with skipWarehouse enabled")
        return enabled_users
    except Exception as e:
        logger.error(f"Error getting enabled users: {str(e)}")
        return []

def get_user_shopify_credentials(username):
    """Get Shopify credentials for a user."""
    logger.info(f"Getting Shopify credentials for user: {username}")
    
    # Find user
    user_info = user_collection.find_one({'username': username})
    
    if not user_info:
        logger.error(f"No user found with username: {username}")
        return None, None
    
    # Get credentials
    store_name = user_info.get('shopifyStoreName', '').strip()
    access_token = user_info.get('shopifyAccessToken', '').strip()
    
    # Validate credentials
    if not store_name or not access_token:
        logger.error(f"Missing Shopify credentials for user {username}")
        return None, None
    
    # Clean store name
    store_name = store_name.replace('.myshopify.com', '')
    store_name = store_name.strip()
    
    logger.info(f"Found Shopify credentials for {username}. Store: {store_name}")
    return store_name, access_token

def get_shopify_location(store_name, access_token):
    """Get the first active location ID from Shopify."""
    try:
        location_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/locations.json"
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": access_token
        }
        response = requests.get(location_url, headers=headers)
        response.raise_for_status()

        locations = [loc for loc in response.json().get('locations', []) if loc['active']]
        if not locations:
            logger.error("No active locations found")
            return None

        location = locations[0]
        logger.info(f"Using location: {location['name']} (ID: {location['id']})")
        return location['id']
    except Exception as e:
        logger.error(f"Error getting Shopify location: {str(e)}")
        return None

def extract_condition(variant_title):
    """Extract condition from variant title."""
    title = variant_title.lower()
    for pattern, condition in CONDITION_PATTERNS.items():
        if pattern.search(title):
            return condition
    return 'nm'  # Default to NM if no condition found

# Define condition patterns for extraction
CONDITION_PATTERNS = {
    re.compile(r'near\s*mint|nm\b', re.I): 'nm',
    re.compile(r'lightly\s*played|lp\b', re.I): 'lp',
    re.compile(r'moderately\s*played|mp\b', re.I): 'mp',
    re.compile(r'heavily\s*played|hp\b', re.I): 'hp',
    re.compile(r'damaged|dm\b|dmg\b', re.I): 'dm'
}

def determine_printing_type(variant_title, valid_subtypes):
    """
    Determine the printing type from variant title and valid subtypes.
    """
    if not variant_title:
        return 'Normal'

    # Clean up variant title
    variant_title = variant_title.lower()
    variant_title = re.sub(r'\s+', ' ', variant_title)
    
    # Remove condition prefixes
    for cond in ['near mint', 'nm', 'lightly played', 'lp', 'moderately played', 'mp', 
                'heavily played', 'hp', 'damaged', 'dmg']:
        variant_title = variant_title.replace(cond, '').strip()
    variant_title = variant_title.strip('- ').strip()
    
    # First try exact match against valid subtypes
    for subtype in valid_subtypes:
        if subtype and variant_title == subtype.lower():
            return subtype
    
    # Then try partial matches
    if 'cold foil' in variant_title:
        return 'Cold Foil'
    if 'rainbow foil' in variant_title:
        return 'Rainbow Foil'
    if 'reverse holofoil' in variant_title or 'reverse holo' in variant_title:
        return 'Reverse Holofoil'
    if 'etched foil' in variant_title:
        return 'Etched Foil'
    if 'etched' in variant_title:
        return 'Etched'
    if 'gilded' in variant_title:
        return 'Gilded'
    if any(s in variant_title for s in ['holofoil', 'holo foil']):
        return 'Holofoil'
    if '1st edition' in variant_title:
        return '1st Edition'
    if 'unlimited' in variant_title:
        return 'Unlimited'
    if 'foil' in variant_title:
        for subtype in valid_subtypes:
            if subtype and 'foil' in subtype.lower():
                return subtype
        return 'Foil'
    
    # If no specific match found, return Normal
    return 'Normal'

def associate_shopify_ids(username, store_name, access_token):
    """Associate inventory records with Shopify IDs using robust matching logic."""
    logger.info(f"Associating Shopify IDs for user: {username}")
    
    stats = {
        "total_inventory_items": 0,
        "matched_items": 0,
        "skipped_items": 0,
        "errors": 0,
        "items_with_issues": []
    }
    
    try:
        # Get inventory items without Shopify IDs
        inventory_items = list(inventory_collection.find({
            'username': username,
            'skuId': {'$exists': True},
            '$or': [
                {'customFields.shopify_inventory_item_id': {'$exists': False}},
                {'customFields.shopify_variant_id': {'$exists': False}},
                {'customFields.shopify_product_id': {'$exists': False}}
            ]
        }))
        
        stats["total_inventory_items"] = len(inventory_items)
        logger.info(f"Found {len(inventory_items)} inventory items without Shopify IDs")
        
        if not inventory_items:
            log_to_mongodb(username, "associate_ids", stats)
            return []
        
        # Process each inventory item
        processed_items = []
        for item in inventory_items:
            try:
                sku_id = item.get('skuId')
                
                # Log detailed information about the item being processed
                logger.info(f"Processing inventory item: {item['_id']}")
                logger.info(f"  SKU ID: {sku_id}")
                logger.info(f"  Quantity: {item.get('quantity', 0)}")
                
                # Try direct SKU match first - most reliable
                shopify_product = shProducts_collection.find_one({
                    'username': username,
                    'variants.sku': str(sku_id)
                })
                
                if not shopify_product:
                    logger.warning(f"No Shopify product found with SKU {sku_id}")
                    stats["skipped_items"] += 1
                    stats["items_with_issues"].append({
                        "sku_id": sku_id,
                        "issue": "No matching Shopify product found"
                    })
                    continue
                
                matching_variant = None
                match_type = None
                
                # Find the matching variant by SKU
                variant = next((v for v in shopify_product.get('variants', []) 
                              if v.get('sku') == str(sku_id)), None)
                if variant:
                    matching_variant = variant
                    match_type = 'skuId'
                    logger.info(f"Found direct SKU match for {sku_id}")
                
                # If we found a matching variant, update the inventory record
                if matching_variant and shopify_product:
                    # Update inventory record with correct Shopify IDs
                    result = inventory_collection.update_one(
                        {'_id': item['_id']},
                        {'$set': {
                            'customFields.shopify_product_id': str(shopify_product.get('shopifyProductId', shopify_product.get('id'))),
                            'customFields.shopify_variant_id': str(matching_variant.get('id')),
                            'customFields.shopify_inventory_item_id': str(matching_variant.get('inventory_item_id'))
                        }}
                    )
                    
                    if result.modified_count > 0:
                        logger.info(f"Updated Shopify IDs for inventory item: {sku_id} (match type: {match_type})")
                        processed_items.append({
                            'inventory_id': str(item['_id']),
                            'sku_id': sku_id,
                            'inventory_item_id': matching_variant.get('inventory_item_id'),
                            'quantity': item.get('quantity', 0)
                        })
                        stats["matched_items"] += 1
                    else:
                        logger.warning(f"Failed to update inventory record for SKU {sku_id}")
                        stats["errors"] += 1
                        stats["items_with_issues"].append({
                            "sku_id": sku_id,
                            "issue": "Failed to update inventory record"
                        })
                else:
                    logger.warning(f"No matching variant found for SKU ID: {sku_id}")
                    stats["skipped_items"] += 1
                    stats["items_with_issues"].append({
                        "sku_id": sku_id,
                        "issue": "No matching variant found in Shopify product"
                    })
            except Exception as e:
                error_msg = str(e)
                logger.error(f"Error processing item {item.get('_id')}: {error_msg}")
                stats["errors"] += 1
                stats["items_with_issues"].append({
                    "sku_id": item.get('skuId', 'Unknown'),
                    "issue": f"Error: {error_msg}"
                })
        
        logger.info(f"Associated Shopify IDs for {len(processed_items)} inventory items")
        
        # Remove the full items_with_issues from stats to avoid overly large log entries
        issues_summary = []
        for issue in stats["items_with_issues"]:
            issues_summary.append({
                "sku_id": issue["sku_id"],
                "issue": issue["issue"]
            })
        
        log_stats = {**stats}
        log_stats["items_with_issues"] = issues_summary
        
        # Log the operation to MongoDB
        log_to_mongodb(username, "associate_ids", log_stats)
        
        return processed_items
    
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Error associating Shopify IDs: {error_msg}")
        
        # Log the error to MongoDB
        log_to_mongodb(username, "associate_ids", stats, status="failed", error=error_msg)
        
        return []

def create_batch_file(username, items, location_id):
    """Create a batch file for inventory adjustments."""
    if not os.path.exists('batches'):
        os.makedirs('batches')
    
    filename = f"batches/{username}_batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jsonl"
    
    with open(filename, 'w') as f:
        for item in items:
            # Create inventory adjustment
            update = {
                "location_id": location_id,
                "inventory_item_id": item['inventory_item_id'],
                "available_adjustment": item['quantity']
            }
            f.write(json.dumps(update) + '\n')
    
    return filename

def adjust_inventory_level(store_name, access_token, inventory_item_id, location_id, adjustment):
    """Adjust inventory level on Shopify."""
    try:
        url = f"https://{store_name}.myshopify.com/admin/api/2023-04/inventory_levels/adjust.json"
        headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': access_token
        }
        payload = {
            'location_id': location_id,
            'inventory_item_id': inventory_item_id,
            'available_adjustment': adjustment
        }
        
        logger.info(f"Sending inventory adjustment to Shopify:")
        logger.info(f"  URL: {url}")
        logger.info(f"  Inventory Item ID: {inventory_item_id}")
        logger.info(f"  Location ID: {location_id}")
        logger.info(f"  Adjustment: {adjustment}")
        
        response = requests.post(url, json=payload, headers=headers)
        
        if response.status_code == 200:
            logger.info(f"Shopify inventory adjustment successful")
            logger.info(f"  Response: {response.json()}")
            return True
        else:
            logger.error(f"Shopify inventory adjustment failed with status code: {response.status_code}")
            logger.error(f"  Response: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Error adjusting inventory level: {str(e)}")
        return False

def push_to_shopify(username, store_name, access_token, items_to_push):
    """Push inventory updates to Shopify and delete records after successful push."""
    logger.info(f"Pushing inventory updates to Shopify for user: {username}")
    
    stats = {
        "total_items": len(items_to_push) if items_to_push else 0,
        "successful_pushes": 0,
        "failed_pushes": 0,
        "deleted_records": 0,
        "skipped_records": 0,
        "records_with_issues": []
    }
    
    if not items_to_push:
        logger.info("No items to push to Shopify")
        log_to_mongodb(username, "push_to_shopify", stats)
        return
    
    try:
        # Get location ID
        location_id = get_shopify_location(store_name, access_token)
        if not location_id:
            logger.error("Failed to get location ID")
            log_to_mongodb(username, "push_to_shopify", stats, status="failed", error="Failed to get location ID")
            return
        
        # Create batch file for tracking
        batch_file = create_batch_file(username, items_to_push, location_id)
        logger.info(f"Created batch file: {batch_file}")
        
        # Process each item individually with rate limiting
        for item in items_to_push:
            inventory_id = item['inventory_id']
            inventory_item_id = item['inventory_item_id']
            sku_id = item.get('sku_id', 'Unknown')
            quantity = item.get('quantity', 0)
            
            # Log detailed information about the item being processed
            logger.info(f"Processing inventory item: {inventory_id}")
            logger.info(f"  SKU ID: {sku_id}")
            logger.info(f"  Shopify Inventory Item ID: {inventory_item_id}")
            logger.info(f"  Quantity: {quantity}")
            
            # Verify the inventory item still exists in MongoDB
            inventory_item = inventory_collection.find_one({'_id': ObjectId(inventory_id)})
            if not inventory_item:
                logger.warning(f"Inventory item {inventory_id} no longer exists in database, skipping")
                stats["skipped_records"] += 1
                stats["records_with_issues"].append({
                    "inventory_id": inventory_id,
                    "sku_id": sku_id,
                    "issue": "Record no longer exists in database"
                })
                continue
            
            # Verify the item has all required Shopify IDs
            if not inventory_item_id:
                logger.error(f"Missing Shopify inventory_item_id for item: {inventory_id}")
                stats["failed_pushes"] += 1
                stats["records_with_issues"].append({
                    "inventory_id": inventory_id,
                    "sku_id": sku_id,
                    "issue": "Missing Shopify inventory_item_id"
                })
                continue
            
            # Push to Shopify
            success = adjust_inventory_level(
                store_name, 
                access_token, 
                inventory_item_id, 
                location_id, 
                quantity
            )
            
            # Rate limiting: Wait 0.5 seconds between API calls (max 2 per second)
            time.sleep(0.5)
            
            if success:
                logger.info(f"Successfully adjusted inventory for item: {inventory_id}")
                
                # Delete this specific record after confirmed success
                delete_result = inventory_collection.delete_one({'_id': ObjectId(inventory_id)})
                
                if delete_result.deleted_count > 0:
                    logger.info(f"Deleted inventory record {inventory_id} after successful Shopify sync")
                    stats["deleted_records"] += 1
                else:
                    logger.warning(f"Failed to delete inventory record {inventory_id} after successful Shopify sync")
                    stats["records_with_issues"].append({
                        "inventory_id": inventory_id,
                        "sku_id": sku_id,
                        "issue": "Failed to delete record after successful push"
                    })
                
                stats["successful_pushes"] += 1
            else:
                logger.error(f"Failed to adjust inventory for item: {inventory_id}")
                stats["failed_pushes"] += 1
                stats["records_with_issues"].append({
                    "inventory_id": inventory_id,
                    "sku_id": sku_id,
                    "issue": "Shopify API call failed"
                })
        
        # Log the operation to MongoDB with detailed information
        status = "success" if stats["failed_pushes"] == 0 else "partial"
        
        # Remove the full records_with_issues from stats to avoid overly large log entries
        issues_summary = []
        for issue in stats["records_with_issues"]:
            issues_summary.append({
                "sku_id": issue.get("sku_id", "Unknown"),
                "issue": issue["issue"]
            })
        
        log_stats = {**stats}
        log_stats["records_with_issues"] = issues_summary
        
        log_to_mongodb(username, "push_to_shopify", log_stats, status=status)
    
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Error pushing to Shopify: {error_msg}")
        
        # Log the error to MongoDB
        log_to_mongodb(username, "push_to_shopify", stats, status="failed", error=error_msg)

def process_inventory():
    """Process inventory for all enabled users."""
    logger.info("Starting Shopify Inventory Processor")
    
    overall_stats = {
        "total_users": 0,
        "successful_users": 0,
        "failed_users": 0,
        "total_items_processed": 0,
        "total_items_pushed": 0
    }
    
    try:
        # Get all users with skipWarehouse enabled
        enabled_users = get_enabled_users()
        overall_stats["total_users"] = len(enabled_users)
        
        if not enabled_users:
            logger.info("No users with skipWarehouse enabled found")
            log_to_mongodb("system", "process_inventory", overall_stats)
            return
        
        # Process each enabled user
        for username in enabled_users:
            logger.info(f"Processing user: {username}")
            user_stats = {
                "items_processed": 0,
                "items_pushed": 0
            }
            
            try:
                # Get Shopify credentials
                store_name, access_token = get_user_shopify_credentials(username)
                
                if not store_name or not access_token:
                    logger.error(f"Failed to get Shopify credentials for user: {username}")
                    overall_stats["failed_users"] += 1
                    log_to_mongodb(username, "process_inventory", user_stats, status="failed", 
                                  error="Missing Shopify credentials")
                    continue
                
                # Step 1: Associate inventory records with Shopify IDs
                processed_items = associate_shopify_ids(username, store_name, access_token)
                user_stats["items_processed"] = len(processed_items)
                overall_stats["total_items_processed"] += len(processed_items)
                
                # Step 2: Push inventory updates to Shopify
                push_to_shopify(username, store_name, access_token, processed_items)
                user_stats["items_pushed"] = len(processed_items)
                overall_stats["total_items_pushed"] += len(processed_items)
                
                logger.info(f"Completed processing for user: {username}")
                overall_stats["successful_users"] += 1
                
                # Log user-specific summary
                log_to_mongodb(username, "process_summary", user_stats)
                
            except Exception as e:
                logger.error(f"Error processing user {username}: {str(e)}")
                overall_stats["failed_users"] += 1
                log_to_mongodb(username, "process_inventory", user_stats, status="failed", error=str(e))
    
    except Exception as e:
        logger.error(f"Critical error in processing: {str(e)}")
        log_to_mongodb("system", "process_inventory", overall_stats, status="failed", error=str(e))
    finally:
        # Log overall summary
        log_to_mongodb("system", "process_inventory", overall_stats)
        logger.info("Processing cycle completed")

def main():
    """Main function that runs the inventory processor every 5 minutes."""
    try:
        logger.info("Starting Shopify Inventory Processor Service")
        logger.info("This service will run every 5 minutes")
        
        while True:
            # Run the inventory processor
            process_inventory()
            
            # Wait for 5 minutes (300 seconds) before running again
            next_run = datetime.now() + timedelta(minutes=5)
            logger.info(f"Next run scheduled at: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
            time.sleep(300)
            
    except KeyboardInterrupt:
        logger.info("Service stopped by user")
    except Exception as e:
        logger.error(f"Critical error in main thread: {str(e)}")
    finally:
        logger.info("Service stopped")

if __name__ == "__main__":
    main()