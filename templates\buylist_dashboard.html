{% extends "base.html" %}
{% block title %}Buylist Dashboard{% endblock %}
{% block content %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/setup.css') }}">

<div class="container-fluid dashboard-container">
    {% if not current_user.subscription or current_user.subscription.name == 'Free' %}
    <div class="alert alert-primary mt-1 mb-2 py-2 d-flex justify-content-between align-items-center" role="alert" style="font-size: 0.9rem;">
        <div class="text-center w-100">
            <i class="fas fa-star me-1"></i>
            <strong>Upgrade your plan to unlock all features!</strong> Get access to advanced analytics, premium tools, and more.
        </div>
        <a href="{{ url_for('activation.activating') }}" class="btn btn-primary btn-sm py-1 px-2">
            Upgrade Now
        </a>
    </div>
    {% endif %}

    <!-- Buylist Dashboard Header -->
    <div class="setup-header">
        <h1><i class="fas fa-shopping-basket me-2"></i>Buylist Dashboard</h1>
        <p class="text-muted">Manage your buylist settings, orders, and hotlist</p>
    </div>

    <!-- Buylist Options Grid -->
    <div class="setup-grid">
        <!-- Buylist Orders Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-clipboard-list"></i>
                <h2>Buylist Orders</h2>
            </div>
            <div class="setup-card-body">
                <p>View and manage your buylist orders. Process customer orders, track status, and manage inventory.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Process customer orders</li>
                    <li><i class="fas fa-check-circle"></i> Track order status</li>
                    <li><i class="fas fa-check-circle"></i> Manage inventory</li>
                    <li><i class="fas fa-check-circle"></i> View order history</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('buylist.buylist_orders') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>View Orders
                </a>
            </div>
        </div>

        <!-- Buylist Settings Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-cog"></i>
                <h2>Buylist Settings</h2>
            </div>
            <div class="setup-card-body">
                <p>Configure your buylist settings. Set up game percentages, conditions, and other preferences.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Game percentages</li>
                    <li><i class="fas fa-check-circle"></i> Condition settings</li>
                    <li><i class="fas fa-check-circle"></i> Pricing preferences</li>
                    <li><i class="fas fa-check-circle"></i> General configuration</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('user_settings.view_edit_settings') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>Manage Settings
                </a>
            </div>
        </div>

        <!-- Advanced Rules Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-sliders-h"></i>
                <h2>Advanced Rules</h2>
            </div>
            <div class="setup-card-body">
                <p>Create and manage advanced buylist rules. Set up custom pricing rules for specific cards, sets, or rarities.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Custom pricing rules</li>
                    <li><i class="fas fa-check-circle"></i> Set-specific rules</li>
                    <li><i class="fas fa-check-circle"></i> Rarity-based pricing</li>
                    <li><i class="fas fa-check-circle"></i> Card-specific adjustments</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('advanced_rules.view_edit_advanced_rules') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>Manage Rules
                </a>
            </div>
        </div>

        <!-- Create Buylist Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-plus-circle"></i>
                <h2>Create Buylist</h2>
            </div>
            <div class="setup-card-body">
                <p>Create a new buylist for customers. Add cards, set prices, and manage your buylist inventory.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Add cards to buylist</li>
                    <li><i class="fas fa-check-circle"></i> Set competitive prices</li>
                    <li><i class="fas fa-check-circle"></i> Manage inventory</li>
                    <li><i class="fas fa-check-circle"></i> Publish to customers</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="https://kiosk.tcgsync.com/{{ current_user.username }}" target="_blank" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>Create Buylist
                </a>
            </div>
        </div>

        <!-- Hotlist Card -->
        <div class="setup-card">
            <div class="setup-card-header">
                <i class="fas fa-fire"></i>
                <h2>Hotlist</h2>
            </div>
            <div class="setup-card-body">
                <p>Manage your hotlist of high-demand cards. Set special pricing for cards you're actively seeking.</p>
                <ul class="setup-features-list">
                    <li><i class="fas fa-check-circle"></i> Add cards to hotlist</li>
                    <li><i class="fas fa-check-circle"></i> Set premium pricing</li>
                    <li><i class="fas fa-check-circle"></i> Track high-demand cards</li>
                    <li><i class="fas fa-check-circle"></i> Prioritize acquisitions</li>
                </ul>
            </div>
            <div class="setup-card-footer">
                <a href="{{ url_for('hotlist.hotlist_page') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>Manage Hotlist
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    /* Setup Dashboard Specific Styles */
    .setup-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .setup-header h1 {
        font-size: 2rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
    }
    
    .setup-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }
    
    .setup-card {
        background: linear-gradient(135deg, rgba(26, 32, 44, 0.8), rgba(45, 55, 72, 0.8));
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.05);
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    
    .setup-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .setup-card-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .setup-card-header i {
        font-size: 2rem;
        color: var(--primary-color);
    }
    
    .setup-card-header h2 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #ffffff;
        margin: 0;
    }
    
    .setup-card-body {
        padding: 1.5rem;
        flex-grow: 1;
    }
    
    .setup-card-body p {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 1.5rem;
    }
    
    .setup-features-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .setup-features-list li {
        margin-bottom: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .setup-features-list li i {
        color: var(--success-color);
        font-size: 0.9rem;
    }
    
    .setup-card-footer {
        padding: 1.5rem;
        border-top: 1px solid rgba(255, 255, 255, 0.05);
        text-align: center;
    }
    
    .setup-card-footer .btn {
        width: 100%;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .setup-card-footer .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .setup-grid {
            grid-template-columns: 1fr;
        }
        
        .setup-header h1 {
            font-size: 1.75rem;
        }
    }
</style>
{% endblock %}
