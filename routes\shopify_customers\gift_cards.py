from flask import jsonify, request
from flask_login import login_required, current_user
from models.user_model import User
from models.customer_notes_model import CustomerNote
from datetime import datetime
import requests
from bson import Decimal128

def init_routes(bp, mongo_client):
    @bp.route('/shopify/customers/api/customer/<customer_id>/gift-card', methods=['POST'])
    @login_required
    def create_gift_card(customer_id):
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            data = request.json
            if not data or 'amount' not in data:
                return jsonify({"error": "Amount is required"}), 400

            shopify_access_token = user.shopifyAccessToken
            shopify_store_name = user.shopifyStoreName
            
            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            # Create gift card in Shopify
            shopify_data = {
                "gift_card": {
                    "note": data.get('note', ''),
                    "initial_value": data['amount'],
                    "customer_id": customer_id
                }
            }

            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards.json"
            response = requests.post(url, headers=headers, json=shopify_data)

            if response.status_code != 201:
                return jsonify({"error": f"Failed to create gift card in Shopify: {response.text}"}), 500

            gift_card_data = response.json()['gift_card']

            # Store in MongoDB
            gift_card_doc = {
                'id': str(gift_card_data['id']),
                'code': gift_card_data['code'],
                'balance': float(gift_card_data.get('balance', 0) or 0),
                'customer_id': customer_id,
                'created_at': datetime.utcnow(),
                'last_used_at': None,
                'note': data.get('note', ''),
                'username': current_user.username
            }

            mongo_client['test']['shGiftCards'].insert_one(gift_card_doc)

            return jsonify({
                "message": "Gift card created successfully",
                "gift_card": gift_card_doc
            }), 201
        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @bp.route('/shopify/customers/api/customer/<customer_id>/gift-cards', methods=['GET'])
    @login_required
    def get_customer_gift_cards(customer_id):
        try:
            # Get gift cards from MongoDB
            gift_cards = list(mongo_client['test']['shGiftCards'].find(
                {
                    'customer_id': customer_id,
                    'username': current_user.username
                },
                {
                    '_id': 0,
                    'code': 1,
                    'balance': 1,
                    'created_at': 1,
                    'last_used_at': 1,
                    'note': 1
                }
            ))

            # Format dates
            for card in gift_cards:
                if 'created_at' in card:
                    card['created_at'] = card['created_at'].isoformat()
                if 'last_used_at' in card and card['last_used_at']:
                    card['last_used_at'] = card['last_used_at'].isoformat()

            return jsonify({
                "gift_cards": gift_cards
            })
        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500
            
    @bp.route('/shopify/customers/api/customer/<customer_id>/gift-card/<gift_card_code>/adjust', methods=['POST'])
    @login_required
    def adjust_gift_card(customer_id, gift_card_code):
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            data = request.json
            if not data or 'amount' not in data or 'is_increase' not in data or 'staff_name' not in data:
                return jsonify({"error": "Amount, adjustment type, and staff name are required"}), 400

            amount = float(data['amount'])
            adjustment_amount = amount if data['is_increase'] else -amount
            
            # Get current gift card from MongoDB
            gift_card = mongo_client['test']['shGiftCards'].find_one({
                'code': gift_card_code,
                'customer_id': customer_id,
                'username': current_user.username
            })
            
            if not gift_card:
                return jsonify({"error": "Gift card not found"}), 404

            current_balance = float(gift_card['balance'])
            new_balance = current_balance + adjustment_amount
            
            if new_balance < 0:
                return jsonify({"error": "Cannot reduce balance below 0"}), 400

            # Adjust gift card in Shopify
            shopify_access_token = user.shopifyAccessToken
            shopify_store_name = user.shopifyStoreName
            
            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            shopify_data = {
                "gift_card_adjustment": {
                    "amount": adjustment_amount,
                    "note": data.get('note', f"Balance adjusted by {adjustment_amount}")
                }
            }

            # Get gift card from Shopify to ensure we have the latest data
            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards/search.json?query={gift_card_code}"
            response = requests.get(url, headers=headers)
            
            if response.status_code != 200:
                return jsonify({"error": f"Failed to fetch gift card from Shopify: {response.text}"}), 500
                
            shopify_gift_cards = response.json().get('gift_cards', [])
            if not shopify_gift_cards:
                return jsonify({"error": "Gift card not found in Shopify"}), 404
                
            shopify_gift_card = shopify_gift_cards[0]
            
            # Make the adjustment
            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards/{shopify_gift_card['id']}/adjustments.json"
            response = requests.post(url, headers=headers, json=shopify_data)

            if response.status_code != 201:
                return jsonify({"error": f"Failed to adjust gift card in Shopify: {response.text}"}), 500

            # Update balance in MongoDB
            mongo_client['test']['shGiftCards'].update_one(
                {'code': gift_card_code},
                {
                    '$set': {
                        'balance': Decimal128(str(new_balance)),
                        'last_used_at': datetime.utcnow()
                    }
                }
            )

            # Save note if provided
            if data.get('note'):
                note_text = f"Gift card balance {'increased' if data['is_increase'] else 'decreased'} by {formatCurrency(amount)}\nNote: {data['note']}"
                customer_note = CustomerNote()
                customer_note.create_note(
                    mongo_client['test'],
                    customer_id,
                    note_text,
                    current_user.username,
                    data['staff_name']
                )

            return jsonify({
                "message": "Gift card adjusted successfully",
                "new_balance": new_balance
            }), 200
            
        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500
