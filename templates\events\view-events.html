{% extends "base.html" %}
{% block title %}View Events{% endblock %}
{% block content %}
<style>
    /* Set the background color to match dashboard */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
    }

    .main-content {
        background-color: #6b21a8;
    }
    
    /* Table styling */
    .events-table {
        background-color: rgba(25, 25, 39, 0.8);
        border-radius: 8px;
        overflow: hidden;
    }
    
    .events-table th {
        background-color: rgba(231, 76, 60, 0.2);
        color: white;
        border-color: rgba(255, 255, 255, 0.1);
        font-weight: 600;
    }
    
    .events-table td {
        color: white;
        border-color: rgba(255, 255, 255, 0.1);
        vertical-align: middle;
    }
    
    .events-table tbody tr {
        transition: all 0.2s ease;
    }
    
    .events-table tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
    
    /* Status badges */
    .badge-upcoming {
        background-color: #3498db;
    }
    
    .badge-active {
        background-color: #2ecc71;
    }
    
    .badge-completed {
        background-color: #95a5a6;
    }
    
    .badge-canceled {
        background-color: #e74c3c;
    }
    
    /* Event type badges */
    .badge-tournament {
        background-color: #9b59b6;
    }
    
    .badge-pre-release {
        background-color: #f1c40f;
        color: #2c3e50;
    }
    
    .badge-draft {
        background-color: #e67e22;
    }
    
    .badge-sealed {
        background-color: #16a085;
    }
    
    .badge-casual {
        background-color: #3498db;
    }
    
    .badge-championship {
        background-color: #e74c3c;
    }
    
    .badge-other {
        background-color: #95a5a6;
    }
    
    /* Action buttons */
    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    /* Empty state */
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: rgba(255, 255, 255, 0.7);
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: rgba(255, 255, 255, 0.3);
    }
</style>

<div class="container mt-5">
    <div class="card" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
<div class="card-header d-flex align-items-center justify-content-between" style="background-color: rgba(231, 76, 60, 0.2); border-bottom: 1px solid rgba(255, 255, 255, 0.05);">
            <div class="d-flex align-items-center">
                <div class="icon-wrapper me-3" style="width: 40px; height: 40px; border-radius: 10px; background-color: rgba(231, 76, 60, 0.3); display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-calendar-alt" style="color: #e74c3c; font-size: 20px;"></i>
                </div>
                <h3 class="text-white mb-0">View Events</h3>
            </div>
            <div>
                <button id="bulkDeleteBtn" class="btn btn-danger btn-sm me-2" style="display: none;">
                    <i class="fas fa-trash me-1"></i> Delete Selected
                </button>
                <a href="{{ url_for('events.create_events') }}" class="btn btn-success btn-sm">
                    <i class="fas fa-plus-circle me-1"></i> Create Event
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if events and events|length > 0 %}
                <div class="table-responsive">
                    <table class="table events-table">
                        <thead>
                            <tr>
                                <th width="40">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAllEvents">
                                    </div>
                                </th>
                                <th>Event</th>
                                <th>Image</th>
                                <th>Date & Time</th>
                                <th>Type</th>
                                <th>Capacity</th>
                                <th>Price</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for event in events %}
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input event-checkbox" type="checkbox" 
                                                   value="{{ event.id }}" 
                                                   data-event-title="{{ event.title }}">
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ event.title }}</strong>
                                        {% if event.is_recurring and event.is_recurrence_parent %}
                                            <span class="badge bg-info ms-2">Recurring</span>
                                        {% endif %}
                                        <br>
                                        <small class="text-white-50">
                                            {% if event.custom_game %}
                                                {{ event.custom_game_name }}
                                            {% else %}
                                                {{ event.game }}
                                            {% endif %}
                                        </small>
                                    </td>
                                    <td>
                                        {% if event.event_image_url %}
                                            <img src="{{ event.event_image_url }}"
                                                 alt="{{ event.title }} Image"
                                                 class="img-fluid rounded"
                                                 style="max-width: 60px; max-height: 40px; object-fit: cover; cursor: pointer;"
                                                 onclick="showImageModal('{{ event.event_image_url }}', '{{ event.title }}')">
                                        {% else %}
                                            <span class="text-white-50">
                                                <i class="fas fa-image"></i> No image
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ event.start_datetime.strftime('%d %b %Y') }}<br>
                                        <small class="text-white-50">{{ event.start_datetime.strftime('%I:%M %p') }}</small>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ event.event_type }}">
                                            {{ event.event_type|title }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ event.attendee_count }}/{{ event.max_attendees }}
                                        </span>
                                        attendees
                                    </td>
                                    <td>
                                        £{{ event.ticket_price }}
                                        {% if event.cost_per_attendee > 0 %}
                                            <br>
                                            <small class="text-white-50">
                                                Cost: £{{ event.cost_per_attendee }}
                                            </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ event.status }}">
                                            {{ event.status|title }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('events.edit_event', event_id=event.id) }}" class="btn btn-sm btn-outline-light btn-action" title="Edit Event">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('events.event_attendees', event_id=event.id) }}" class="btn btn-sm btn-outline-primary btn-action" title="Manage Attendees">
                                                <i class="fas fa-users"></i>
                                            </a>
                                            {% if event.shopify_product_url %}
                                                <a href="{{ event.shopify_product_url }}" target="_blank" class="btn btn-sm btn-outline-info btn-action" title="View in Shopify">
                                                    <i class="fas fa-shopping-cart"></i>
                                                </a>
                                            {% endif %}
                                            <button type="button" class="btn btn-sm btn-outline-danger btn-action delete-event" 
                                                    data-event-id="{{ event.id }}" 
                                                    data-event-title="{{ event.title }}"
                                                    title="Delete Event">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-calendar-alt"></i>
                    <h4>No Events Found</h4>
                    <p>You haven't created any events yet.</p>
                    <a href="{{ url_for('events.create_events') }}" class="btn btn-success mt-3">
                        <i class="fas fa-plus-circle me-2"></i>Create Your First Event
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteEventModal" tabindex="-1" aria-labelledby="deleteEventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="background-color: #1e293b; color: white; border: none; border-radius: 12px;">
            <div class="modal-header" style="border-bottom-color: rgba(255, 255, 255, 0.1);">
                <h5 class="modal-title" id="deleteEventModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the event "<span id="eventTitleToDelete"></span>"?</p>
                <p class="text-danger">This action cannot be undone. The associated Shopify product will also be deleted.</p>
                
                <div id="recurringWarning" class="alert alert-warning mt-3" style="display: none; background-color: rgba(241, 196, 15, 0.1); border-color: rgba(241, 196, 15, 0.2); color: white;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span>This is a recurring event. All instances of this event will be deleted.</span>
                </div>
            </div>
            <div class="modal-footer" style="border-top-color: rgba(255, 255, 255, 0.1);">
                <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete Event</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1" aria-labelledby="bulkDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="background-color: #1e293b; color: white; border: none; border-radius: 12px;">
            <div class="modal-header" style="border-bottom-color: rgba(255, 255, 255, 0.1);">
                <h5 class="modal-title" id="bulkDeleteModalLabel">Confirm Bulk Delete</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <span id="selectedEventsCount">0</span> selected events?</p>
                <p class="text-danger">This action cannot be undone. All associated Shopify products will also be deleted.</p>
                
                <div id="bulkRecurringWarning" class="alert alert-warning mt-3" style="display: none; background-color: rgba(241, 196, 15, 0.1); border-color: rgba(241, 196, 15, 0.2); color: white;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span>One or more recurring events are selected. All instances of these events will be deleted.</span>
                </div>
            </div>
            <div class="modal-footer" style="border-top-color: rgba(255, 255, 255, 0.1);">
                <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmBulkDelete">Delete Selected Events</button>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content" style="background-color: #1e293b; color: white; border: none; border-radius: 12px;">
            <div class="modal-header" style="border-bottom-color: rgba(255, 255, 255, 0.1);">
                <h5 class="modal-title" id="imageModalLabel">Event Image</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Event Image" class="img-fluid rounded" style="max-width: 100%; max-height: 70vh;">
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <!-- Toasts will be added here dynamically -->
</div>

<script>
    // Image modal function
    function showImageModal(imageUrl, eventTitle) {
        const modal = new bootstrap.Modal(document.getElementById('imageModal'));
        const modalImage = document.getElementById('modalImage');
        const modalTitle = document.getElementById('imageModalLabel');

        modalImage.src = imageUrl;
        modalImage.alt = eventTitle + ' Image';
        modalTitle.textContent = eventTitle + ' - Event Image';

        modal.show();
    }

    // Toast notification function
    function showToast(message, type = 'info') {
        const toastContainer = document.querySelector('.toast-container');
        
        // Create toast element
        const toastEl = document.createElement('div');
        toastEl.className = `toast align-items-center text-white border-0`;
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');
        
        // Set background color based on type
        let bgColor, icon;
        switch(type) {
            case 'success':
                bgColor = '#2ecc71';
                icon = 'fa-check-circle';
                break;
            case 'error':
                bgColor = '#e74c3c';
                icon = 'fa-exclamation-circle';
                break;
            case 'warning':
                bgColor = '#f1c40f';
                icon = 'fa-exclamation-triangle';
                break;
            default: // info
                bgColor = '#3498db';
                icon = 'fa-info-circle';
        }
        
        toastEl.style.backgroundColor = bgColor;
        
        // Create toast content
        toastEl.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas ${icon} me-2"></i> ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        // Add toast to container
        toastContainer.appendChild(toastEl);
        
        // Initialize and show toast
        const toast = new bootstrap.Toast(toastEl, {
            animation: true,
            autohide: true,
            delay: 5000
        });
        toast.show();
        
        // Remove toast after it's hidden
        toastEl.addEventListener('hidden.bs.toast', function() {
            toastEl.remove();
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Bulk selection handling
        const selectAllCheckbox = document.getElementById('selectAllEvents');
        const eventCheckboxes = document.querySelectorAll('.event-checkbox');
        const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
        const bulkDeleteModal = new bootstrap.Modal(document.getElementById('bulkDeleteModal'));
        const selectedEventsCountSpan = document.getElementById('selectedEventsCount');
        const bulkRecurringWarning = document.getElementById('bulkRecurringWarning');
        const confirmBulkDeleteBtn = document.getElementById('confirmBulkDelete');
        
        // Function to update the bulk delete button visibility
        function updateBulkDeleteButton() {
            const checkedCount = document.querySelectorAll('.event-checkbox:checked').length;
            bulkDeleteBtn.style.display = checkedCount > 0 ? 'inline-block' : 'none';
        }
        
        // Handle select all checkbox
        selectAllCheckbox.addEventListener('change', function() {
            eventCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkDeleteButton();
        });
        
        // Handle individual checkboxes
        eventCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // If any checkbox is unchecked, uncheck the "select all" checkbox
                if (!this.checked) {
                    selectAllCheckbox.checked = false;
                } else {
                    // Check if all checkboxes are checked
                    const allChecked = Array.from(eventCheckboxes).every(cb => cb.checked);
                    selectAllCheckbox.checked = allChecked;
                }
                updateBulkDeleteButton();
            });
        });
        
        // Handle bulk delete button click
        bulkDeleteBtn.addEventListener('click', function() {
            const selectedEvents = document.querySelectorAll('.event-checkbox:checked');
            const selectedCount = selectedEvents.length;
            
            if (selectedCount === 0) return;
            
            // Update the count in the modal
            selectedEventsCountSpan.textContent = selectedCount;
            
            // Check if any recurring events are selected
            let hasRecurringEvents = false;
            selectedEvents.forEach(checkbox => {
                const row = checkbox.closest('tr');
                if (row.querySelector('.badge.bg-info') !== null) {
                    hasRecurringEvents = true;
                }
            });
            
            // Show warning for recurring events
            bulkRecurringWarning.style.display = hasRecurringEvents ? 'block' : 'none';
            
            // Show the modal
            bulkDeleteModal.show();
        });
        
        // Handle confirm bulk delete button
        confirmBulkDeleteBtn.addEventListener('click', function() {
            const selectedEvents = document.querySelectorAll('.event-checkbox:checked');
            const eventIds = Array.from(selectedEvents).map(checkbox => checkbox.value);
            
            if (eventIds.length === 0) return;
            
            // Show loading state
            confirmBulkDeleteBtn.disabled = true;
            confirmBulkDeleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Deleting...';
            
            // Send bulk delete request
            fetch('{{ url_for("events.bulk_delete_events") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ event_ids: eventIds })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hide modal
                    bulkDeleteModal.hide();
                    
                    // Show success message
                    showToast(data.message, 'success');
                    
                    // Reload page to reflect changes after a short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    // Show error message
                    showToast(`Error: ${data.message}`, 'error');
                    
                    // Reset button
                    confirmBulkDeleteBtn.disabled = false;
                    confirmBulkDeleteBtn.innerHTML = 'Delete Selected Events';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('An error occurred while deleting the events. Please try again.', 'error');
                
                // Reset button
                confirmBulkDeleteBtn.disabled = false;
                confirmBulkDeleteBtn.innerHTML = 'Delete Selected Events';
            });
        });
        
        // Delete event handling
        const deleteButtons = document.querySelectorAll('.delete-event');
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteEventModal'));
        const eventTitleSpan = document.getElementById('eventTitleToDelete');
        const confirmDeleteBtn = document.getElementById('confirmDelete');
        const recurringWarning = document.getElementById('recurringWarning');
        
        let eventIdToDelete = null;
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Get event details from data attributes
                eventIdToDelete = this.getAttribute('data-event-id');
                const eventTitle = this.getAttribute('data-event-title');
                
                // Set the event title in the modal
                eventTitleSpan.textContent = eventTitle;
                
                // Check if this is a recurring event parent
                const row = this.closest('tr');
                const isRecurring = row.querySelector('.badge.bg-info') !== null;
                
                // Show warning for recurring events
                recurringWarning.style.display = isRecurring ? 'block' : 'none';
                
                // Show the modal
                deleteModal.show();
            });
        });
        
        // Handle confirm delete button
        confirmDeleteBtn.addEventListener('click', function() {
            if (!eventIdToDelete) return;
            
            // Show loading state
            confirmDeleteBtn.disabled = true;
            confirmDeleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Deleting...';
            
            // Send delete request
            fetch(`{{ url_for('events.delete_event', event_id='EVENT_ID_PLACEHOLDER') }}`.replace('EVENT_ID_PLACEHOLDER', eventIdToDelete), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hide modal
                    deleteModal.hide();
                    
                    // Show success message
                    showToast(data.message, 'success');
                    
                    // Reload page to reflect changes after a short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    // Show error message
                    showToast(`Error: ${data.message}`, 'error');
                    
                    // Reset button
                    confirmDeleteBtn.disabled = false;
                    confirmDeleteBtn.innerHTML = 'Delete Event';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('An error occurred while deleting the event. Please try again.', 'error');
                
                // Reset button
                confirmDeleteBtn.disabled = false;
                confirmDeleteBtn.innerHTML = 'Delete Event';
            });
        });
    });
</script>
{% endblock %}
